#include <iostream>
#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <iomanip>

class OffsetValidator {
private:
    HANDLE processHandle;
    DWORD processId;
    
public:
    OffsetValidator() : processHandle(nullptr), processId(0) {}
    
    ~OffsetValidator() {
        if (processHandle) {
            CloseHandle(processHandle);
        }
    }
    
    bool AttachToProcess(const std::string& processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return false;
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        bool found = false;
        if (Process32First(snapshot, &pe32)) {
            do {
                // Convert WCHAR to char for comparison
                char exeFileName[MAX_PATH];
                WideCharToMultiByte(CP_UTF8, 0, (LPCWCH)pe32.szExeFile, -1, exeFileName, MAX_PATH, NULL, NULL);
                if (_stricmp(exeFileName, processName.c_str()) == 0) {
                    processId = pe32.th32ProcessID;
                    found = true;
                    break;
                }
            } while (Process32Next(snapshot, &pe32));
        }
        
        CloseHandle(snapshot);
        if (!found) return false;
        
        processHandle = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, FALSE, processId);
        return processHandle != nullptr;
    }
    
    bool ValidateLocalPlayerOffset(uintptr_t offset) {
        std::cout << "\n=== VALIDANDO OFFSET LocalPlayer ===" << std::endl;
        std::cout << "Offset: 0x" << std::hex << offset << std::dec << std::endl;
        
        // Teste 1: Verificar se o endereço é acessível
        uintptr_t playerPtr = 0;
        SIZE_T bytesRead;
        
        if (!ReadProcessMemory(processHandle, (LPCVOID)offset, &playerPtr, sizeof(playerPtr), &bytesRead)) {
            std::cout << "❌ FALHA: Não foi possível ler o endereço" << std::endl;
            return false;
        }
        
        if (bytesRead != sizeof(playerPtr)) {
            std::cout << "❌ FALHA: Leitura incompleta" << std::endl;
            return false;
        }
        
        std::cout << "✅ Endereço acessível" << std::endl;
        std::cout << "Player Pointer: 0x" << std::hex << playerPtr << std::dec << std::endl;
        
        // Teste 2: Verificar se o ponteiro é válido
        if (playerPtr < 0x10000 || playerPtr > 0x7FFFFFFFFFFF) {
            std::cout << "❌ FALHA: Ponteiro inválido (muito baixo ou muito alto)" << std::endl;
            return false;
        }
        
        std::cout << "✅ Ponteiro em range válido" << std::endl;
        
        // Teste 3: Tentar ler coordenadas (offset +0x90)
        float coords[3] = {0};
        if (ReadProcessMemory(processHandle, (LPCVOID)(playerPtr + 0x90), coords, sizeof(coords), &bytesRead)) {
            std::cout << "✅ Coordenadas lidas com sucesso" << std::endl;
            std::cout << "   X: " << std::fixed << std::setprecision(2) << coords[0] << std::endl;
            std::cout << "   Y: " << std::fixed << std::setprecision(2) << coords[1] << std::endl;
            std::cout << "   Z: " << std::fixed << std::setprecision(2) << coords[2] << std::endl;
            
            // Verificar se as coordenadas estão em um range válido do GTA V
            if (coords[0] >= -4000 && coords[0] <= 4000 &&
                coords[1] >= -4000 && coords[1] <= 4000 &&
                coords[2] >= -1000 && coords[2] <= 2000) {
                std::cout << "✅ Coordenadas em range válido do mapa" << std::endl;
            } else {
                std::cout << "⚠️  AVISO: Coordenadas fora do range esperado" << std::endl;
            }
        } else {
            std::cout << "❌ FALHA: Não foi possível ler coordenadas" << std::endl;
            return false;
        }
        
        // Teste 4: Tentar ler vida (offset +0x280)
        float health = 0;
        if (ReadProcessMemory(processHandle, (LPCVOID)(playerPtr + 0x280), &health, sizeof(health), &bytesRead)) {
            std::cout << "✅ Vida lida com sucesso: " << std::fixed << std::setprecision(1) << health << std::endl;
            
            if (health >= 0 && health <= 1000) {
                std::cout << "✅ Vida em range válido" << std::endl;
            } else {
                std::cout << "⚠️  AVISO: Vida fora do range esperado (0-1000)" << std::endl;
            }
        } else {
            std::cout << "⚠️  AVISO: Não foi possível ler vida (pode ser offset diferente)" << std::endl;
        }
        
        // Teste 5: Verificar se os dados mudam (teste de movimento)
        std::cout << "\n🔄 TESTE DE MOVIMENTO:" << std::endl;
        std::cout << "Mova-se no jogo e pressione Enter para continuar..." << std::endl;
        std::cin.get();
        
        float newCoords[3] = {0};
        if (ReadProcessMemory(processHandle, (LPCVOID)(playerPtr + 0x90), newCoords, sizeof(newCoords), &bytesRead)) {
            float distance = sqrt(pow(newCoords[0] - coords[0], 2) + 
                                pow(newCoords[1] - coords[1], 2) + 
                                pow(newCoords[2] - coords[2], 2));
            
            std::cout << "Novas coordenadas:" << std::endl;
            std::cout << "   X: " << std::fixed << std::setprecision(2) << newCoords[0] << std::endl;
            std::cout << "   Y: " << std::fixed << std::setprecision(2) << newCoords[1] << std::endl;
            std::cout << "   Z: " << std::fixed << std::setprecision(2) << newCoords[2] << std::endl;
            std::cout << "Distância movida: " << std::fixed << std::setprecision(2) << distance << " unidades" << std::endl;
            
            if (distance > 0.1) {
                std::cout << "✅ SUCESSO: Coordenadas mudaram - offset é dinâmico!" << std::endl;
                return true;
            } else {
                std::cout << "⚠️  AVISO: Coordenadas não mudaram - pode ser offset estático" << std::endl;
            }
        }
        
        return true;
    }
    
    void TestOffsetStability() {
        std::cout << "\n=== TESTE DE ESTABILIDADE ===" << std::endl;
        std::cout << "Este teste verifica se o offset permanece válido ao longo do tempo" << std::endl;
        
        uintptr_t localPlayerOffset = 0x7fffa2db9c28; // Offset encontrado
        
        for (int i = 0; i < 5; i++) {
            std::cout << "\nTeste " << (i + 1) << "/5:" << std::endl;
            
            uintptr_t playerPtr = 0;
            SIZE_T bytesRead;
            
            if (ReadProcessMemory(processHandle, (LPCVOID)localPlayerOffset, &playerPtr, sizeof(playerPtr), &bytesRead)) {
                float coords[3] = {0};
                if (ReadProcessMemory(processHandle, (LPCVOID)(playerPtr + 0x90), coords, sizeof(coords), &bytesRead)) {
                    std::cout << "✅ Leitura bem-sucedida - Pos: (" 
                              << std::fixed << std::setprecision(1) 
                              << coords[0] << ", " << coords[1] << ", " << coords[2] << ")" << std::endl;
                } else {
                    std::cout << "❌ Falha na leitura das coordenadas" << std::endl;
                }
            } else {
                std::cout << "❌ Falha na leitura do ponteiro" << std::endl;
            }
            
            if (i < 4) {
                std::cout << "Aguardando 2 segundos..." << std::endl;
                Sleep(2000);
            }
        }
    }
};

int main() {
    SetConsoleTitleA("FiveM Offset Validator");
    
    std::cout << "=== VALIDADOR DE OFFSETS FIVEM ===" << std::endl;
    std::cout << "Este programa testa se os offsets encontrados são válidos" << std::endl;
    std::cout << "====================================" << std::endl;
    
    OffsetValidator validator;
    
    std::cout << "\n[~] Conectando ao FiveM..." << std::endl;
    if (!validator.AttachToProcess("FiveM.exe")) {
        std::cerr << "❌ ERRO: Não foi possível conectar ao FiveM" << std::endl;
        std::cerr << "Certifique-se de que o FiveM está rodando" << std::endl;
        std::cout << "\nPressione Enter para sair..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    std::cout << "✅ Conectado ao FiveM com sucesso!" << std::endl;
    
    // Validar o offset LocalPlayer encontrado
    uintptr_t localPlayerOffset = 0x7fffa2db9c28;
    
    bool isValid = validator.ValidateLocalPlayerOffset(localPlayerOffset);
    
    std::cout << "\n=== RESULTADO FINAL ===" << std::endl;
    if (isValid) {
        std::cout << "✅ OFFSET VÁLIDO!" << std::endl;
        std::cout << "O offset LocalPlayer parece estar funcionando corretamente" << std::endl;
    } else {
        std::cout << "❌ OFFSET INVÁLIDO!" << std::endl;
        std::cout << "O offset pode estar incorreto ou o jogo mudou" << std::endl;
    }
    
    // Teste de estabilidade
    std::cout << "\nDeseja executar teste de estabilidade? (y/n): ";
    char choice;
    std::cin >> choice;
    std::cin.ignore();
    
    if (choice == 'y' || choice == 'Y') {
        validator.TestOffsetStability();
    }
    
    std::cout << "\nPressione Enter para sair..." << std::endl;
    std::cin.get();
    
    return 0;
}
