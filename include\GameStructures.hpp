#pragma once

#include <windows.h>
#include <DirectXMath.h>

using namespace DirectX;

namespace FiveMCheat {

    // Forward declarations
    struct Vector3;
    struct Vector4;
    struct Matrix4x4;

    // Basic math structures
    struct Vector2 {
        float x, y;
        
        Vector2() : x(0), y(0) {}
        Vector2(float x, float y) : x(x), y(y) {}
        
        Vector2 operator+(const Vector2& other) const { return Vector2(x + other.x, y + other.y); }
        Vector2 operator-(const Vector2& other) const { return Vector2(x - other.x, y - other.y); }
        Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
        
        float Distance(const Vector2& other) const {
            float dx = x - other.x;
            float dy = y - other.y;
            return sqrt(dx * dx + dy * dy);
        }
    };

    struct Vector3 {
        float x, y, z;
        
        Vector3() : x(0), y(0), z(0) {}
        Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
        
        Vector3 operator+(const Vector3& other) const { return Vector3(x + other.x, y + other.y, z + other.z); }
        Vector3 operator-(const Vector3& other) const { return Vector3(x - other.x, y - other.y, z - other.z); }
        Vector3 operator*(float scalar) const { return Vector3(x * scalar, y * scalar, z * scalar); }
        
        float Distance(const Vector3& other) const {
            float dx = x - other.x;
            float dy = y - other.y;
            float dz = z - other.z;
            return sqrt(dx * dx + dy * dy + dz * dz);
        }
        
        float Length() const { return sqrt(x * x + y * y + z * z); }
        Vector3 Normalize() const {
            float len = Length();
            return len > 0 ? Vector3(x / len, y / len, z / len) : Vector3();
        }
    };

    struct Vector4 {
        float x, y, z, w;
        
        Vector4() : x(0), y(0), z(0), w(0) {}
        Vector4(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
    };

    struct Matrix4x4 {
        float m[4][4];
        
        Matrix4x4() {
            memset(m, 0, sizeof(m));
            m[0][0] = m[1][1] = m[2][2] = m[3][3] = 1.0f;
        }
    };

    // Game entity structures
    struct Entity {
        char pad_0000[32]; // 0x0000
        Vector3 position; // 0x0020
        char pad_002C[4]; // 0x002C
        Vector3 rotation; // 0x0030
        char pad_003C[4]; // 0x003C
        Vector3 velocity; // 0x0040
        char pad_004C[4]; // 0x004C
        float health; // 0x0050
        float maxHealth; // 0x0054
        char pad_0058[8]; // 0x0058
        uint32_t model; // 0x0060
        char pad_0064[12]; // 0x0064
        bool isAlive; // 0x0070
        char pad_0071[15]; // 0x0071
    }; // Size: 0x0080

    struct Player {
        char pad_0000[16]; // 0x0000
        Entity* entity; // 0x0010
        char pad_0018[8]; // 0x0018
        char name[64]; // 0x0020
        char pad_0060[32]; // 0x0060
        int32_t playerId; // 0x0080
        char pad_0084[4]; // 0x0084
        float armor; // 0x0088
        char pad_008C[4]; // 0x008C
        bool isInVehicle; // 0x0090
        char pad_0091[7]; // 0x0091
        Entity* vehicle; // 0x0098
        char pad_00A0[32]; // 0x00A0
        Vector3 aimDirection; // 0x00C0
        char pad_00CC[20]; // 0x00CC
    }; // Size: 0x00E0

    struct Vehicle {
        Entity base; // 0x0000
        char pad_0080[16]; // 0x0080
        float engineHealth; // 0x0090
        float bodyHealth; // 0x0094
        char pad_0098[8]; // 0x0098
        float speed; // 0x00A0
        char pad_00A4[12]; // 0x00A4
        bool isEngineRunning; // 0x00B0
        char pad_00B1[15]; // 0x00B1
        Vector3 wheelPositions[4]; // 0x00C0
        char pad_00F0[16]; // 0x00F0
    }; // Size: 0x0100

    struct Weapon {
        char pad_0000[8]; // 0x0000
        uint32_t hash; // 0x0008
        char pad_000C[4]; // 0x000C
        int32_t ammo; // 0x0010
        int32_t maxAmmo; // 0x0014
        char pad_0018[8]; // 0x0018
        float damage; // 0x0020
        float range; // 0x0024
        char pad_0028[24]; // 0x0028
    }; // Size: 0x0040

    struct Camera {
        char pad_0000[16]; // 0x0000
        Vector3 position; // 0x0010
        char pad_001C[4]; // 0x001C
        Vector3 rotation; // 0x0020
        char pad_002C[4]; // 0x002C
        float fov; // 0x0030
        char pad_0034[12]; // 0x0034
        Matrix4x4 viewMatrix; // 0x0040
        Matrix4x4 projectionMatrix; // 0x0080
        char pad_00C0[64]; // 0x00C0
    }; // Size: 0x0100

    struct World {
        char pad_0000[32]; // 0x0000
        Entity** entityList; // 0x0020
        int32_t entityCount; // 0x0028
        int32_t maxEntities; // 0x002C
        Player** playerList; // 0x0030
        int32_t playerCount; // 0x0038
        int32_t maxPlayers; // 0x003C
        Vehicle** vehicleList; // 0x0040
        int32_t vehicleCount; // 0x0048
        int32_t maxVehicles; // 0x004C
        char pad_0050[16]; // 0x0050
        Camera* camera; // 0x0060
        char pad_0068[24]; // 0x0068
    }; // Size: 0x0080

    // Game manager structures
    struct PlayerManager {
        char pad_0000[8]; // 0x0000
        Player* localPlayer; // 0x0008
        char pad_0010[8]; // 0x0010
        Player** players; // 0x0018
        int32_t playerCount; // 0x0020
        int32_t maxPlayers; // 0x0024
        char pad_0028[24]; // 0x0028
    }; // Size: 0x0040

    struct EntityManager {
        char pad_0000[16]; // 0x0000
        Entity** entities; // 0x0010
        int32_t entityCount; // 0x0018
        int32_t maxEntities; // 0x001C
        char pad_0020[32]; // 0x0020
    }; // Size: 0x0040

    // Offsets for different game versions
    namespace Offsets {
        // Base offsets
        constexpr uintptr_t WORLD_PTR = 0x2685B08;
        constexpr uintptr_t PLAYER_MANAGER = 0x2685B10;
        constexpr uintptr_t ENTITY_MANAGER = 0x2685B18;
        constexpr uintptr_t CAMERA_MANAGER = 0x2685B20;
        constexpr uintptr_t VIEW_MATRIX = 0x2685B28;

        // Entity offsets
        constexpr uintptr_t ENTITY_POSITION = 0x20;
        constexpr uintptr_t ENTITY_ROTATION = 0x30;
        constexpr uintptr_t ENTITY_VELOCITY = 0x40;
        constexpr uintptr_t ENTITY_HEALTH = 0x50;
        constexpr uintptr_t ENTITY_MAX_HEALTH = 0x54;
        constexpr uintptr_t ENTITY_MODEL = 0x60;

        // Player offsets
        constexpr uintptr_t PLAYER_ENTITY = 0x10;
        constexpr uintptr_t PLAYER_NAME = 0x20;
        constexpr uintptr_t PLAYER_ID = 0x80;
        constexpr uintptr_t PLAYER_ARMOR = 0x88;
        constexpr uintptr_t PLAYER_IN_VEHICLE = 0x90;
        constexpr uintptr_t PLAYER_VEHICLE = 0x98;

        // Vehicle offsets
        constexpr uintptr_t VEHICLE_ENGINE_HEALTH = 0x90;
        constexpr uintptr_t VEHICLE_BODY_HEALTH = 0x94;
        constexpr uintptr_t VEHICLE_SPEED = 0xA0;
        constexpr uintptr_t VEHICLE_ENGINE_RUNNING = 0xB0;

        // Camera offsets
        constexpr uintptr_t CAMERA_POSITION = 0x10;
        constexpr uintptr_t CAMERA_ROTATION = 0x20;
        constexpr uintptr_t CAMERA_FOV = 0x30;
        constexpr uintptr_t CAMERA_VIEW_MATRIX = 0x40;
    }

} // namespace FiveMCheat
