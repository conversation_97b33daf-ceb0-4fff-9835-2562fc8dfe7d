@echo off
echo ================================
echo SKECH FiveM Cheat Menu Builder
echo ================================

REM Check for Visual Studio
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] Visual Studio não encontrado. Configurando ambiente...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64
    if %ERRORLEVEL% NEQ 0 (
        echo [-] Falha ao configurar Visual Studio
        pause
        exit /b 1
    )
    echo [+] Ambiente configurado!
) else (
    echo [+] Visual Studio encontrado!
)

REM Create vendor directory for ImGui
if not exist "vendor" (
    mkdir vendor
    echo [+] Diretório vendor criado
)

if not exist "vendor\imgui" (
    mkdir vendor\imgui
    echo [+] Diretório ImGui criado
)

REM Check if ImGui files exist
if not exist "vendor\imgui\imgui.h" (
    echo [!] ImGui não encontrado. Você precisa baixar o ImGui manualmente.
    echo [!] Baixe de: https://github.com/ocornut/imgui
    echo [!] Extraia os arquivos para: vendor\imgui\
    echo [!] Arquivos necessários:
    echo     - imgui.h
    echo     - imgui.cpp
    echo     - imgui_demo.cpp
    echo     - imgui_draw.cpp
    echo     - imgui_tables.cpp
    echo     - imgui_widgets.cpp
    echo     - imgui_impl_win32.h
    echo     - imgui_impl_win32.cpp
    echo     - imgui_impl_dx11.h
    echo     - imgui_impl_dx11.cpp
    echo     - imconfig.h
    echo     - imgui_internal.h
    echo     - imstb_rectpack.h
    echo     - imstb_textedit.h
    echo     - imstb_truetype.h
    echo.
    echo [!] Após baixar, execute este script novamente.
    pause
    exit /b 1
) else (
    echo [+] ImGui encontrado!
)

REM Create build directory
if not exist "build_menu" (
    mkdir build_menu
    echo [+] Diretório build_menu criado
) else (
    echo [+] Usando diretório build_menu existente
)

cd build_menu

REM Configure with CMake
echo [~] Configurando projeto com CMake...
copy ..\CMakeLists_Menu.txt CMakeLists.txt >nul
cmake -G "Visual Studio 17 2022" -A x64 ..

if %ERRORLEVEL% NEQ 0 (
    echo [-] Falha na configuração do CMake
    cd ..
    pause
    exit /b 1
)

echo [+] Configuração concluída!

REM Build the project
echo [~] Compilando menu...
cmake --build . --config Release

if %ERRORLEVEL% EQU 0 (
    echo [+] Compilação concluída com sucesso!
    echo [+] Executável criado: build_menu\Release\FiveMCheatMenu.exe
    echo.
    echo ================================
    echo MENU COMPILADO COM SUCESSO!
    echo ================================
    echo.
    echo Para usar o menu:
    echo 1. Execute: build_menu\Release\FiveMCheatMenu.exe
    echo 2. Pressione INSERT para abrir/fechar
    echo 3. Interface baseada no design do Figma
    echo.
) else (
    echo [-] Falha na compilação!
)

cd ..
pause
