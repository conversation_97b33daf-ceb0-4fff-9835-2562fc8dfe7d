@echo off
echo ========================================
echo  Configurando Dependências - FiveM Menu
echo ========================================

:: Criar diretórios necessários
if not exist "vendor" mkdir vendor
if not exist "resources" mkdir resources
if not exist "resources\fonts" mkdir resources\fonts

cd vendor

:: Baixar Dear ImGui
echo Baixando Dear ImGui...
if not exist "imgui" (
    git clone https://github.com/ocornut/imgui.git
    if %ERRORLEVEL% neq 0 (
        echo Erro ao baixar Dear ImGui!
        pause
        exit /b 1
    )
)

:: Baixar GLEW
echo Baixando GLEW...
if not exist "glew" (
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/nigels-com/glew/releases/download/glew-2.2.0/glew-2.2.0.zip' -OutFile 'glew.zip'"
    powershell -Command "Expand-Archive -Path 'glew.zip' -DestinationPath '.'"
    ren glew-2.2.0 glew
    del glew.zip
)

cd ..

:: Criar arquivo de configuração padrão
echo Criando arquivo de configuração...
echo {> resources\config.json
echo   "general": {>> resources\config.json
echo     "theme": "dark",>> resources\config.json
echo     "language": "pt-BR",>> resources\config.json
echo     "auto_connect": true>> resources\config.json
echo   },>> resources\config.json
echo   "hotkeys": [>> resources\config.json
echo     {>> resources\config.json
echo       "action": "toggle_menu",>> resources\config.json
echo       "key": 290,>> resources\config.json
echo       "ctrl": false,>> resources\config.json
echo       "shift": false,>> resources\config.json
echo       "alt": false>> resources\config.json
echo     }>> resources\config.json
echo   ],>> resources\config.json
echo   "player": {>> resources\config.json
echo     "godMode": false,>> resources\config.json
echo     "invisible": false,>> resources\config.json
echo     "noClip": false,>> resources\config.json
echo     "walkSpeed": 1.0,>> resources\config.json
echo     "runSpeed": 1.0>> resources\config.json
echo   },>> resources\config.json
echo   "vehicle": {>> resources\config.json
echo     "autoRepair": false,>> resources\config.json
echo     "infiniteFuel": false,>> resources\config.json
echo     "noCollision": false,>> resources\config.json
echo     "speedMultiplier": 1.0>> resources\config.json
echo   },>> resources\config.json
echo   "visual": {>> resources\config.json
echo     "esp": false,>> resources\config.json
echo     "aimbot": false,>> resources\config.json
echo     "espDistance": 500.0,>> resources\config.json
echo     "espColor": [1.0, 0.0, 0.0, 1.0]>> resources\config.json
echo   }>> resources\config.json
echo }>> resources\config.json

echo.
echo ========================================
echo   Dependências configuradas com sucesso!
echo ========================================
echo.
echo Próximos passos:
echo 1. Execute build.bat para compilar o projeto
echo 2. Instale GLFW3 usando vcpkg ou baixe manualmente
echo.
echo Para instalar GLFW3 com vcpkg:
echo vcpkg install glfw3:x64-windows
echo.

pause
