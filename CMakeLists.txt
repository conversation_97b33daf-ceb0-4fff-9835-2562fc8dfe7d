cmake_minimum_required(VERSION 3.16)
project(FiveMExternalCheat VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações de build
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Encontrar pacotes necessários
find_package(OpenGL REQUIRED)
find_package(glfw3 REQUIRED)

# Incluir Dear ImGui
set(IMGUI_DIR ${CMAKE_CURRENT_SOURCE_DIR}/vendor/imgui)
file(GLOB IMGUI_SOURCES 
    ${IMGUI_DIR}/*.cpp
    ${IMGUI_DIR}/backends/imgui_impl_glfw.cpp
    ${IMGUI_DIR}/backends/imgui_impl_opengl3.cpp
)

# Incluir GLEW
set(GLEW_DIR ${CMAKE_CURRENT_SOURCE_DIR}/vendor/glew)

# Arquivos fonte do projeto
file(GLOB_RECURSE SOURCES 
    "src/*.cpp"
    "src/*.hpp"
    "include/*.hpp"
)

# Criar executável
add_executable(${PROJECT_NAME} 
    ${SOURCES}
    ${IMGUI_SOURCES}
    ${GLEW_DIR}/src/glew.c
)

# Diretórios de include
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${IMGUI_DIR}
    ${IMGUI_DIR}/backends
    ${GLEW_DIR}/include
    ${OPENGL_INCLUDE_DIRS}
)

# Definições de compilação
target_compile_definitions(${PROJECT_NAME} PRIVATE
    GLEW_STATIC
    IMGUI_IMPL_OPENGL_LOADER_GLEW
)

# Linkar bibliotecas
target_link_libraries(${PROJECT_NAME}
    ${OPENGL_LIBRARIES}
    glfw
)

# Configurações específicas do Windows
if(WIN32)
    target_link_libraries(${PROJECT_NAME} 
        ws2_32
        winmm
        user32
        gdi32
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        advapi32
    )
    
    # Definir como aplicação Windows (sem console)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )
endif()

# Copiar recursos para o diretório de build
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/resources DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Configurações de instalação
install(TARGETS ${PROJECT_NAME} DESTINATION bin)
install(DIRECTORY resources DESTINATION .)
