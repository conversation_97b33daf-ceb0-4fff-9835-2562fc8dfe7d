#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <imgui.h>

namespace FiveMMenu {

    struct Hotkey {
        int key;
        bool ctrl;
        bool shift;
        bool alt;
        std::string action;
    };

    struct Theme {
        ImVec4 primary;
        ImVec4 secondary;
        ImVec4 accent;
        ImVec4 background;
        ImVec4 text;
        ImVec4 textDisabled;
        ImVec4 border;
        ImVec4 success;
        ImVec4 warning;
        ImVec4 error;
    };

    class Config {
    public:
        Config();
        ~Config();

        bool Load(const std::string& filename = "config.json");
        bool Save(const std::string& filename = "config.json");

        // Configurações gerais
        bool GetBool(const std::string& key, bool defaultValue = false);
        int GetInt(const std::string& key, int defaultValue = 0);
        float GetFloat(const std::string& key, float defaultValue = 0.0f);
        std::string GetString(const std::string& key, const std::string& defaultValue = "");

        void SetBool(const std::string& key, bool value);
        void SetInt(const std::string& key, int value);
        void SetFloat(const std::string& key, float value);
        void SetString(const std::string& key, const std::string& value);

        // Hotkeys
        const std::vector<Hotkey>& GetHotkeys() const { return m_hotkeys; }
        void AddHotkey(const Hotkey& hotkey);
        void RemoveHotkey(const std::string& action);
        bool IsHotkeyPressed(const std::string& action);

        // Tema
        const Theme& GetTheme() const { return m_theme; }
        void SetTheme(const Theme& theme) { m_theme = theme; }
        void LoadDefaultTheme();
        void LoadDarkTheme();
        void LoadLightTheme();

        // Configurações específicas
        struct PlayerSettings {
            bool godMode = false;
            bool invisible = false;
            bool noClip = false;
            float walkSpeed = 1.0f;
            float runSpeed = 1.0f;
        } player;

        struct VehicleSettings {
            bool autoRepair = false;
            bool infiniteFuel = false;
            bool noCollision = false;
            float speedMultiplier = 1.0f;
        } vehicle;

        struct VisualSettings {
            bool esp = false;
            bool aimbot = false;
            float espDistance = 500.0f;
            ImVec4 espColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        } visual;

    private:
        std::unordered_map<std::string, std::string> m_settings;
        std::vector<Hotkey> m_hotkeys;
        Theme m_theme;

        void SetDefaultValues();
    };

} // namespace FiveMMenu
