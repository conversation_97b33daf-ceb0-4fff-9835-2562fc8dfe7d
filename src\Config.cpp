#include "Config.hpp"
#include <fstream>
#include <iostream>
#include <GLFW/glfw3.h>

// Biblioteca JSON simples (você pode usar n<PERSON>hmann/json se preferir)
#include <sstream>

namespace FiveMMenu {

    Config::Config() {
        SetDefaultValues();
        LoadDefaultTheme();
    }

    Config::~Config() {
        Save();
    }

    bool Config::Load(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cout << "Arquivo de configuração não encontrado. Usando valores padrão." << std::endl;
            return false;
        }

        // Implementação simples de parsing JSON
        // Em um projeto real, use uma biblioteca como nlohmann/json
        std::string line;
        while (std::getline(file, line)) {
            // Parse básico de configurações
            if (line.find("\"godMode\"") != std::string::npos) {
                player.godMode = line.find("true") != std::string::npos;
            } else if (line.find("\"invisible\"") != std::string::npos) {
                player.invisible = line.find("true") != std::string::npos;
            } else if (line.find("\"noClip\"") != std::string::npos) {
                player.noClip = line.find("true") != std::string::npos;
            } else if (line.find("\"walkSpeed\"") != std::string::npos) {
                size_t pos = line.find(":");
                if (pos != std::string::npos) {
                    std::string value = line.substr(pos + 1);
                    player.walkSpeed = std::stof(value);
                }
            }
            // Adicionar mais parsing conforme necessário
        }

        file.close();
        std::cout << "Configuração carregada de: " << filename << std::endl;
        return true;
    }

    bool Config::Save(const std::string& filename) {
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "Erro ao salvar configuração em: " << filename << std::endl;
            return false;
        }

        // Salvar configurações em formato JSON
        file << "{\n";
        file << "  \"general\": {\n";
        file << "    \"theme\": \"dark\",\n";
        file << "    \"language\": \"pt-BR\",\n";
        file << "    \"auto_connect\": true\n";
        file << "  },\n";
        
        file << "  \"hotkeys\": [\n";
        for (size_t i = 0; i < m_hotkeys.size(); ++i) {
            const auto& hotkey = m_hotkeys[i];
            file << "    {\n";
            file << "      \"action\": \"" << hotkey.action << "\",\n";
            file << "      \"key\": " << hotkey.key << ",\n";
            file << "      \"ctrl\": " << (hotkey.ctrl ? "true" : "false") << ",\n";
            file << "      \"shift\": " << (hotkey.shift ? "true" : "false") << ",\n";
            file << "      \"alt\": " << (hotkey.alt ? "true" : "false") << "\n";
            file << "    }";
            if (i < m_hotkeys.size() - 1) file << ",";
            file << "\n";
        }
        file << "  ],\n";
        
        file << "  \"player\": {\n";
        file << "    \"godMode\": " << (player.godMode ? "true" : "false") << ",\n";
        file << "    \"invisible\": " << (player.invisible ? "true" : "false") << ",\n";
        file << "    \"noClip\": " << (player.noClip ? "true" : "false") << ",\n";
        file << "    \"walkSpeed\": " << player.walkSpeed << ",\n";
        file << "    \"runSpeed\": " << player.runSpeed << "\n";
        file << "  },\n";
        
        file << "  \"vehicle\": {\n";
        file << "    \"autoRepair\": " << (vehicle.autoRepair ? "true" : "false") << ",\n";
        file << "    \"infiniteFuel\": " << (vehicle.infiniteFuel ? "true" : "false") << ",\n";
        file << "    \"noCollision\": " << (vehicle.noCollision ? "true" : "false") << ",\n";
        file << "    \"speedMultiplier\": " << vehicle.speedMultiplier << "\n";
        file << "  },\n";
        
        file << "  \"visual\": {\n";
        file << "    \"esp\": " << (visual.esp ? "true" : "false") << ",\n";
        file << "    \"aimbot\": " << (visual.aimbot ? "true" : "false") << ",\n";
        file << "    \"espDistance\": " << visual.espDistance << ",\n";
        file << "    \"espColor\": [" << visual.espColor.x << ", " << visual.espColor.y << ", " << visual.espColor.z << ", " << visual.espColor.w << "]\n";
        file << "  }\n";
        file << "}\n";

        file.close();
        std::cout << "Configuração salva em: " << filename << std::endl;
        return true;
    }

    bool Config::GetBool(const std::string& key, bool defaultValue) {
        auto it = m_settings.find(key);
        if (it != m_settings.end()) {
            return it->second == "true";
        }
        return defaultValue;
    }

    int Config::GetInt(const std::string& key, int defaultValue) {
        auto it = m_settings.find(key);
        if (it != m_settings.end()) {
            return std::stoi(it->second);
        }
        return defaultValue;
    }

    float Config::GetFloat(const std::string& key, float defaultValue) {
        auto it = m_settings.find(key);
        if (it != m_settings.end()) {
            return std::stof(it->second);
        }
        return defaultValue;
    }

    std::string Config::GetString(const std::string& key, const std::string& defaultValue) {
        auto it = m_settings.find(key);
        if (it != m_settings.end()) {
            return it->second;
        }
        return defaultValue;
    }

    void Config::SetBool(const std::string& key, bool value) {
        m_settings[key] = value ? "true" : "false";
    }

    void Config::SetInt(const std::string& key, int value) {
        m_settings[key] = std::to_string(value);
    }

    void Config::SetFloat(const std::string& key, float value) {
        m_settings[key] = std::to_string(value);
    }

    void Config::SetString(const std::string& key, const std::string& value) {
        m_settings[key] = value;
    }

    void Config::AddHotkey(const Hotkey& hotkey) {
        // Remover hotkey existente com a mesma ação
        RemoveHotkey(hotkey.action);
        m_hotkeys.push_back(hotkey);
    }

    void Config::RemoveHotkey(const std::string& action) {
        m_hotkeys.erase(
            std::remove_if(m_hotkeys.begin(), m_hotkeys.end(),
                [&action](const Hotkey& h) { return h.action == action; }),
            m_hotkeys.end()
        );
    }

    bool Config::IsHotkeyPressed(const std::string& action) {
        for (const auto& hotkey : m_hotkeys) {
            if (hotkey.action == action) {
                bool keyPressed = glfwGetKey(glfwGetCurrentContext(), hotkey.key) == GLFW_PRESS;
                bool ctrlPressed = !hotkey.ctrl || (glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_LEFT_CONTROL) == GLFW_PRESS || 
                                                   glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_RIGHT_CONTROL) == GLFW_PRESS);
                bool shiftPressed = !hotkey.shift || (glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_LEFT_SHIFT) == GLFW_PRESS || 
                                                     glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_RIGHT_SHIFT) == GLFW_PRESS);
                bool altPressed = !hotkey.alt || (glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_LEFT_ALT) == GLFW_PRESS || 
                                                 glfwGetKey(glfwGetCurrentContext(), GLFW_KEY_RIGHT_ALT) == GLFW_PRESS);
                
                return keyPressed && ctrlPressed && shiftPressed && altPressed;
            }
        }
        return false;
    }

    void Config::LoadDefaultTheme() {
        m_theme.primary = ImVec4(0.26f, 0.59f, 0.98f, 1.00f);
        m_theme.secondary = ImVec4(0.20f, 0.20f, 0.20f, 1.00f);
        m_theme.accent = ImVec4(0.33f, 0.67f, 0.86f, 1.00f);
        m_theme.background = ImVec4(0.10f, 0.10f, 0.10f, 1.00f);
        m_theme.text = ImVec4(1.00f, 1.00f, 1.00f, 1.00f);
        m_theme.textDisabled = ImVec4(0.50f, 0.50f, 0.50f, 1.00f);
        m_theme.border = ImVec4(0.19f, 0.19f, 0.19f, 1.00f);
        m_theme.success = ImVec4(0.0f, 0.8f, 0.0f, 1.0f);
        m_theme.warning = ImVec4(1.0f, 0.8f, 0.0f, 1.0f);
        m_theme.error = ImVec4(0.8f, 0.0f, 0.0f, 1.0f);
    }

    void Config::LoadDarkTheme() {
        LoadDefaultTheme(); // O tema padrão já é dark
    }

    void Config::LoadLightTheme() {
        m_theme.primary = ImVec4(0.26f, 0.59f, 0.98f, 1.00f);
        m_theme.secondary = ImVec4(0.90f, 0.90f, 0.90f, 1.00f);
        m_theme.accent = ImVec4(0.33f, 0.67f, 0.86f, 1.00f);
        m_theme.background = ImVec4(0.95f, 0.95f, 0.95f, 1.00f);
        m_theme.text = ImVec4(0.00f, 0.00f, 0.00f, 1.00f);
        m_theme.textDisabled = ImVec4(0.60f, 0.60f, 0.60f, 1.00f);
        m_theme.border = ImVec4(0.70f, 0.70f, 0.70f, 1.00f);
        m_theme.success = ImVec4(0.0f, 0.6f, 0.0f, 1.0f);
        m_theme.warning = ImVec4(0.8f, 0.6f, 0.0f, 1.0f);
        m_theme.error = ImVec4(0.8f, 0.0f, 0.0f, 1.0f);
    }

    void Config::SetDefaultValues() {
        // Hotkeys padrão
        m_hotkeys.clear();
        m_hotkeys.push_back({GLFW_KEY_F1, false, false, false, "toggle_menu"});
        m_hotkeys.push_back({GLFW_KEY_F2, false, false, false, "toggle_player"});
        m_hotkeys.push_back({GLFW_KEY_F3, false, false, false, "toggle_vehicle"});
        m_hotkeys.push_back({GLFW_KEY_F4, false, false, false, "toggle_teleport"});
        m_hotkeys.push_back({GLFW_KEY_F5, false, false, false, "toggle_visual"});

        // Configurações padrão
        SetString("theme", "dark");
        SetString("language", "pt-BR");
        SetBool("auto_connect", true);
        SetBool("save_on_exit", true);
    }

} // namespace FiveMMenu
