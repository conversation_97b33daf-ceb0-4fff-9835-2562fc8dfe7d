#pragma once

#include "GameStructures.hpp"
#include <memory>
#include <vector>
#include <string>
#include <map>

namespace FiveMCheat {

    class ProcessManager;
    class MemoryScanner;

    enum class ESPType {
        Box2D,
        Box3D,
        Skeleton,
        Filled,
        Corner,
        Glow
    };

    enum class ESPInfo {
        Name,
        Health,
        Armor,
        Distance,
        Weapon,
        Money,
        Wanted,
        Vehicle,
        Speed
    };

    struct ESPColor {
        float r, g, b, a;
        
        ESPColor() : r(1.0f), g(1.0f), b(1.0f), a(1.0f) {}
        ESPColor(float r, float g, float b, float a = 1.0f) : r(r), g(g), b(b), a(a) {}
        
        static ESPColor Red() { return ESPColor(1.0f, 0.0f, 0.0f); }
        static ESPColor Green() { return ESPColor(0.0f, 1.0f, 0.0f); }
        static ESPColor Blue() { return ESPColor(0.0f, 0.0f, 1.0f); }
        static ESPColor Yellow() { return ESPColor(1.0f, 1.0f, 0.0f); }
        static ESPColor White() { return ESPColor(1.0f, 1.0f, 1.0f); }
        static ESPColor Black() { return ESPColor(0.0f, 0.0f, 0.0f); }
        static ESPColor Orange() { return ESPColor(1.0f, 0.5f, 0.0f); }
        static ESPColor Purple() { return ESPColor(0.5f, 0.0f, 1.0f); }
    };

    struct ESPSettings {
        // General
        bool enabled = false;
        float maxDistance = 1000.0f;
        bool visibilityCheck = false;
        
        // Players
        bool showPlayers = true;
        ESPType playerESPType = ESPType::Box2D;
        ESPColor playerColorVisible = ESPColor::Green();
        ESPColor playerColorHidden = ESPColor::Red();
        ESPColor teammateColor = ESPColor::Blue();
        bool showTeammates = false;
        
        // NPCs
        bool showNPCs = false;
        ESPType npcESPType = ESPType::Box2D;
        ESPColor npcColor = ESPColor::Yellow();
        
        // Vehicles
        bool showVehicles = true;
        ESPType vehicleESPType = ESPType::Box2D;
        ESPColor vehicleColor = ESPColor::Purple();
        bool showEmptyVehicles = true;
        bool showOccupiedVehicles = true;
        
        // Objects
        bool showObjects = false;
        ESPColor objectColor = ESPColor::White();
        std::vector<std::string> objectFilter;
        
        // Weapons
        bool showWeapons = true;
        ESPColor weaponColor = ESPColor::Orange();
        
        // Money/Items
        bool showMoney = true;
        ESPColor moneyColor = ESPColor::Green();
        bool showItems = false;
        ESPColor itemColor = ESPColor::White();
        
        // Information display
        std::vector<ESPInfo> playerInfo = {ESPInfo::Name, ESPInfo::Health, ESPInfo::Distance};
        std::vector<ESPInfo> vehicleInfo = {ESPInfo::Distance, ESPInfo::Speed};
        bool showHealthBar = true;
        bool showArmorBar = true;
        
        // Visual settings
        float boxThickness = 1.0f;
        float skeletonThickness = 1.0f;
        bool filled = false;
        float fillAlpha = 0.3f;
        bool outlined = true;
        ESPColor outlineColor = ESPColor::Black();
        
        // Text settings
        float fontSize = 12.0f;
        ESPColor textColor = ESPColor::White();
        bool textOutline = true;
        ESPColor textOutlineColor = ESPColor::Black();
        
        // Glow settings
        bool glowEnabled = false;
        float glowIntensity = 1.0f;
        ESPColor glowColor = ESPColor::Red();
        
        // Crosshair
        bool showCrosshair = false;
        ESPColor crosshairColor = ESPColor::White();
        float crosshairSize = 10.0f;
        float crosshairThickness = 1.0f;
        
        // Radar
        bool showRadar = false;
        Vector2 radarPosition = Vector2(100, 100);
        float radarSize = 150.0f;
        float radarRange = 500.0f;
        ESPColor radarBackground = ESPColor(0.0f, 0.0f, 0.0f, 0.5f);
        
        // Performance
        int maxEntities = 1000;
        bool frustumCulling = true;
        bool distanceCulling = true;
    };

    struct ESPEntity {
        uintptr_t entityPtr;
        Vector3 position;
        Vector3 velocity;
        Vector3 boundingBox[8]; // 3D bounding box corners
        Vector2 screenPos;
        Vector2 screenBox[4]; // 2D screen box
        
        // Entity info
        std::string name;
        float health;
        float maxHealth;
        float armor;
        float distance;
        bool isVisible;
        bool isPlayer;
        bool isNPC;
        bool isVehicle;
        bool isWeapon;
        bool isItem;
        bool isTeammate;
        
        // Player specific
        int playerId;
        std::string weaponName;
        int money;
        int wantedLevel;
        
        // Vehicle specific
        std::string vehicleModel;
        float speed;
        bool isOccupied;
        int occupantCount;
        
        // Rendering
        ESPColor color;
        bool shouldRender;
        float alpha;
    };

    class ESP {
    public:
        ESP(std::shared_ptr<ProcessManager> processManager, 
            std::shared_ptr<MemoryScanner> memoryScanner);
        ~ESP();

        // Main functions
        void Update();
        void Render();
        
        // Settings
        ESPSettings& GetSettings() { return m_settings; }
        void SetSettings(const ESPSettings& settings) { m_settings = settings; }
        
        // Control
        void Enable() { m_settings.enabled = true; }
        void Disable() { m_settings.enabled = false; }
        void Toggle() { m_settings.enabled = !m_settings.enabled; }
        bool IsEnabled() const { return m_settings.enabled; }
        
        // Entity management
        std::vector<ESPEntity> GetVisibleEntities();
        void UpdateEntities();
        
        // Rendering functions
        void RenderEntity(const ESPEntity& entity);
        void RenderBox2D(const ESPEntity& entity);
        void RenderBox3D(const ESPEntity& entity);
        void RenderSkeleton(const ESPEntity& entity);
        void RenderFilled(const ESPEntity& entity);
        void RenderCorner(const ESPEntity& entity);
        void RenderGlow(const ESPEntity& entity);
        
        // Information rendering
        void RenderEntityInfo(const ESPEntity& entity);
        void RenderHealthBar(const ESPEntity& entity);
        void RenderArmorBar(const ESPEntity& entity);
        void RenderName(const ESPEntity& entity);
        void RenderDistance(const ESPEntity& entity);
        void RenderWeapon(const ESPEntity& entity);
        
        // UI elements
        void RenderCrosshair();
        void RenderRadar();
        void RenderFOVCircle(float fov);
        
        // Utility functions
        bool IsEntityValid(uintptr_t entityPtr);
        bool IsInScreen(const Vector2& screenPos);
        bool IsInFrustum(const Vector3& worldPos);
        Vector3 WorldToScreen(const Vector3& worldPos);
        Vector2 GetEntityScreenPosition(const ESPEntity& entity);
        
        // Bounding box calculations
        void CalculateBoundingBox(ESPEntity& entity);
        void Calculate2DBox(ESPEntity& entity);
        void Calculate3DBox(ESPEntity& entity);
        
        // Color management
        ESPColor GetEntityColor(const ESPEntity& entity);
        ESPColor InterpolateColor(const ESPColor& color1, const ESPColor& color2, float factor);
        ESPColor GetHealthColor(float health, float maxHealth);
        
        // Performance optimization
        void CullEntities();
        bool ShouldRenderEntity(const ESPEntity& entity);
        
        // Drawing primitives
        void DrawLine(const Vector2& start, const Vector2& end, const ESPColor& color, float thickness = 1.0f);
        void DrawRect(const Vector2& position, const Vector2& size, const ESPColor& color, float thickness = 1.0f);
        void DrawFilledRect(const Vector2& position, const Vector2& size, const ESPColor& color);
        void DrawCircle(const Vector2& center, float radius, const ESPColor& color, float thickness = 1.0f);
        void DrawText(const Vector2& position, const std::string& text, const ESPColor& color, float fontSize = 12.0f);

    private:
        std::shared_ptr<ProcessManager> m_processManager;
        std::shared_ptr<MemoryScanner> m_memoryScanner;
        ESPSettings m_settings;
        
        // Entity lists
        std::vector<ESPEntity> m_entities;
        std::vector<ESPEntity> m_players;
        std::vector<ESPEntity> m_vehicles;
        std::vector<ESPEntity> m_weapons;
        std::vector<ESPEntity> m_items;
        
        // Game state
        uintptr_t m_localPlayer;
        Vector3 m_localPlayerPos;
        Vector3 m_cameraPos;
        Matrix4x4 m_viewMatrix;
        Vector2 m_screenSize;
        
        // Performance tracking
        std::chrono::steady_clock::time_point m_lastUpdate;
        int m_entityCount;
        int m_renderedCount;
        
        // Internal methods
        void UpdateGameState();
        void CollectEntities();
        void ProcessPlayers();
        void ProcessVehicles();
        void ProcessWeapons();
        void ProcessItems();
        
        // Entity validation
        bool IsPlayerValid(uintptr_t playerPtr);
        bool IsVehicleValid(uintptr_t vehiclePtr);
        bool IsWeaponValid(uintptr_t weaponPtr);
        
        // Information extraction
        std::string GetPlayerName(uintptr_t playerPtr);
        std::string GetVehicleModel(uintptr_t vehiclePtr);
        std::string GetWeaponName(uintptr_t weaponPtr);
        float GetEntityHealth(uintptr_t entityPtr);
        float GetEntityArmor(uintptr_t entityPtr);
        Vector3 GetEntityPosition(uintptr_t entityPtr);
        Vector3 GetEntityVelocity(uintptr_t entityPtr);
        
        // Skeleton rendering
        void RenderPlayerSkeleton(const ESPEntity& entity);
        std::vector<std::pair<int, int>> GetSkeletonBones();
        Vector3 GetBonePosition(uintptr_t entityPtr, int boneId);
        
        // Memory addresses and offsets
        uintptr_t m_entityListPtr;
        uintptr_t m_localPlayerPtr;
        uintptr_t m_viewMatrixPtr;
        uintptr_t m_screenSizePtr;
        
        // Bone IDs for skeleton
        static constexpr int BONE_HEAD = 8;
        static constexpr int BONE_NECK = 7;
        static constexpr int BONE_SPINE = 6;
        static constexpr int BONE_PELVIS = 0;
        static constexpr int BONE_LEFT_SHOULDER = 9;
        static constexpr int BONE_LEFT_ELBOW = 10;
        static constexpr int BONE_LEFT_HAND = 11;
        static constexpr int BONE_RIGHT_SHOULDER = 12;
        static constexpr int BONE_RIGHT_ELBOW = 13;
        static constexpr int BONE_RIGHT_HAND = 14;
        static constexpr int BONE_LEFT_HIP = 1;
        static constexpr int BONE_LEFT_KNEE = 2;
        static constexpr int BONE_LEFT_FOOT = 3;
        static constexpr int BONE_RIGHT_HIP = 4;
        static constexpr int BONE_RIGHT_KNEE = 5;
        static constexpr int BONE_RIGHT_FOOT = 6;
    };

} // namespace FiveMCheat
