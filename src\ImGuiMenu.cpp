#include "ImGuiMenu.hpp"
#include <iostream>
#include <tchar.h>

// Global menu instance
std::unique_ptr<ImGuiMenu> g_Menu = nullptr;

ImGuiMenu::ImGuiMenu()
    : hwnd(nullptr), pd3dDevice(nullptr), pd3dDeviceContext(nullptr),
      pSwa<PERSON><PERSON><PERSON><PERSON>(nullptr), pMainRenderTargetView(nullptr),
      showMenu(false), initialized(false), currentTab(0) {

    // Exact Figma design dimensions and colors
    menuSize = ImVec2(1000, 650);

    // Exact colors from Figma design
    primaryColor = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);      // Very dark background #141414
    secondaryColor = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);    // Sidebar background #1E1E1E
    accentColor = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);          // Red/Orange accent #FF4D33
    backgroundColor = ImVec4(0.06f, 0.06f, 0.06f, 0.98f);   // Main background #0F0F0F
    textColor = ImVec4(0.9f, 0.9f, 0.9f, 1.0f);            // Light gray text
}

ImGuiMenu::~ImGuiMenu() {
    Shutdown();
}

bool ImGuiMenu::Initialize() {
    // Create window class
    wc = { sizeof(wc), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr, L"FiveM Cheat", nullptr };
    ::RegisterClassExW(&wc);
    
    // Create window
    hwnd = ::CreateWindowW(wc.lpszClassName, L"FiveM Cheat Menu", 
                          WS_OVERLAPPEDWINDOW, 100, 100, 
                          static_cast<int>(menuSize.x), static_cast<int>(menuSize.y), 
                          nullptr, nullptr, wc.hInstance, nullptr);
    
    if (!hwnd) {
        std::cerr << "Failed to create window" << std::endl;
        return false;
    }
    
    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd)) {
        CleanupDeviceD3D();
        ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
        return false;
    }
    
    // Show window
    ::ShowWindow(hwnd, SW_SHOWDEFAULT);
    ::UpdateWindow(hwnd);
    
    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;
    
    // Setup style
    SetupStyle();
    
    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(pd3dDevice, pd3dDeviceContext);
    
    initialized = true;
    return true;
}

void ImGuiMenu::Shutdown() {
    if (!initialized) return;
    
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
    
    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
    
    initialized = false;
}

void ImGuiMenu::Render() {
    if (!initialized || !showMenu) return;
    
    // Poll and handle messages
    MSG msg;
    while (::PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE)) {
        ::TranslateMessage(&msg);
        ::DispatchMessage(&msg);
        if (msg.message == WM_QUIT) {
            showMenu = false;
            return;
        }
    }
    
    // Start the Dear ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();
    
    // Render main menu
    RenderMainMenu();
    
    // Rendering
    ImGui::Render();
    const float clear_color_with_alpha[4] = { 
        backgroundColor.x * backgroundColor.w, 
        backgroundColor.y * backgroundColor.w, 
        backgroundColor.z * backgroundColor.w, 
        backgroundColor.w 
    };
    pd3dDeviceContext->OMSetRenderTargets(1, &pMainRenderTargetView, nullptr);
    pd3dDeviceContext->ClearRenderTargetView(pMainRenderTargetView, clear_color_with_alpha);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
    
    pSwapChain->Present(1, 0); // Present with vsync
}

void ImGuiMenu::SetupStyle() {
    ImGuiStyle& style = ImGui::GetStyle();

    // Exact Figma design colors
    ImVec4* colors = style.Colors;
    colors[ImGuiCol_Text] = ImVec4(0.9f, 0.9f, 0.9f, 1.0f);                    // Light gray text
    colors[ImGuiCol_TextDisabled] = ImVec4(0.5f, 0.5f, 0.5f, 1.0f);            // Disabled text
    colors[ImGuiCol_WindowBg] = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);             // Main window background
    colors[ImGuiCol_ChildBg] = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);              // Sidebar background
    colors[ImGuiCol_PopupBg] = ImVec4(0.08f, 0.08f, 0.08f, 0.98f);             // Popup background
    colors[ImGuiCol_Border] = ImVec4(0.2f, 0.2f, 0.2f, 0.8f);                  // Borders
    colors[ImGuiCol_BorderShadow] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);            // No shadow
    colors[ImGuiCol_FrameBg] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);              // Input backgrounds
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.18f, 0.18f, 0.18f, 1.0f);       // Hovered inputs
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);           // Active inputs
    colors[ImGuiCol_TitleBg] = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);              // Title background
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);        // Active title
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);     // Collapsed title
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);            // Menu bar
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.08f, 0.08f, 0.08f, 1.0f);          // Scrollbar background
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.3f, 0.3f, 0.3f, 1.0f);           // Scrollbar grab
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.4f, 0.4f, 0.4f, 1.0f);    // Hovered scrollbar
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.5f, 0.5f, 0.5f, 1.0f);     // Active scrollbar
    colors[ImGuiCol_CheckMark] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);               // Red checkmarks
    colors[ImGuiCol_SliderGrab] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);              // Red slider grab
    colors[ImGuiCol_SliderGrabActive] = ImVec4(1.0f, 0.4f, 0.3f, 1.0f);        // Active slider
    colors[ImGuiCol_Button] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);               // Button background
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);           // Hovered button
    colors[ImGuiCol_ButtonActive] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);            // Active button (red)
    colors[ImGuiCol_Header] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);               // Header background
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);           // Hovered header
    colors[ImGuiCol_HeaderActive] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);            // Active header (red)
    colors[ImGuiCol_Separator] = ImVec4(0.25f, 0.25f, 0.25f, 1.0f);            // Separators
    colors[ImGuiCol_SeparatorHovered] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);        // Hovered separator
    colors[ImGuiCol_SeparatorActive] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);         // Active separator
    colors[ImGuiCol_ResizeGrip] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);              // Resize grip
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(1.0f, 0.4f, 0.3f, 1.0f);       // Hovered resize
    colors[ImGuiCol_ResizeGripActive] = ImVec4(1.0f, 0.5f, 0.4f, 1.0f);        // Active resize
    colors[ImGuiCol_Tab] = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);                  // Tab background
    colors[ImGuiCol_TabHovered] = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);              // Hovered tab
    colors[ImGuiCol_TabActive] = ImVec4(1.0f, 0.3f, 0.2f, 1.0f);               // Active tab (red)
    colors[ImGuiCol_TabUnfocused] = ImVec4(0.12f, 0.12f, 0.12f, 1.0f);         // Unfocused tab
    colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);   // Unfocused active

    // Exact Figma design style settings
    style.WindowRounding = 12.0f;        // Rounded corners like in design
    style.ChildRounding = 8.0f;          // Rounded child windows
    style.FrameRounding = 6.0f;          // Rounded inputs/buttons
    style.PopupRounding = 8.0f;          // Rounded popups
    style.ScrollbarRounding = 12.0f;     // Rounded scrollbars
    style.GrabRounding = 6.0f;           // Rounded sliders
    style.TabRounding = 6.0f;            // Rounded tabs
    style.WindowBorderSize = 0.0f;       // No window borders
    style.ChildBorderSize = 0.0f;        // No child borders
    style.PopupBorderSize = 1.0f;        // Thin popup borders
    style.FrameBorderSize = 0.0f;        // No frame borders
    style.TabBorderSize = 0.0f;          // No tab borders
    style.WindowPadding = ImVec2(0.0f, 0.0f);      // No window padding for full control
    style.FramePadding = ImVec2(12.0f, 8.0f);      // Comfortable frame padding
    style.ItemSpacing = ImVec2(8.0f, 8.0f);        // Consistent spacing
    style.ItemInnerSpacing = ImVec2(8.0f, 6.0f);   // Inner spacing
    style.IndentSpacing = 25.0f;                    // Sidebar indentation
    style.ScrollbarSize = 14.0f;                    // Thin scrollbars
    style.GrabMinSize = 12.0f;                      // Minimum grab size
}

void ImGuiMenu::RenderMainMenu() {
    // Set window size and position to fill screen
    ImGui::SetNextWindowSize(menuSize, ImGuiCond_Always);
    ImGui::SetNextWindowPos(ImVec2(0, 0), ImGuiCond_Always);

    // Main window flags - exactly like Figma design
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoResize |
                                   ImGuiWindowFlags_NoMove |
                                   ImGuiWindowFlags_NoCollapse |
                                   ImGuiWindowFlags_NoTitleBar |
                                   ImGuiWindowFlags_NoScrollbar;

    if (ImGui::Begin("SKECH [beta] for Fivem", &showMenu, window_flags)) {

        // Create the exact layout from Figma: Sidebar + Main Content
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 window_pos = ImGui::GetWindowPos();
        ImVec2 window_size = ImGui::GetWindowSize();

        // Sidebar dimensions (exactly like Figma)
        float sidebar_width = 220.0f;

        // Draw sidebar background
        draw_list->AddRectFilled(
            window_pos,
            ImVec2(window_pos.x + sidebar_width, window_pos.y + window_size.y),
            IM_COL32(30, 30, 30, 255), // Dark sidebar background
            12.0f, ImDrawFlags_RoundCornersLeft
        );

        // Draw main content background
        draw_list->AddRectFilled(
            ImVec2(window_pos.x + sidebar_width, window_pos.y),
            ImVec2(window_pos.x + window_size.x, window_pos.y + window_size.y),
            IM_COL32(20, 20, 20, 255), // Darker main background
            12.0f, ImDrawFlags_RoundCornersRight
        );

        // Sidebar content
        ImGui::SetCursorPos(ImVec2(0, 0));
        if (ImGui::BeginChild("Sidebar", ImVec2(sidebar_width, window_size.y), false, ImGuiWindowFlags_NoScrollbar)) {

            // Header with logo/title
            ImGui::SetCursorPosY(20);
            ImGui::SetCursorPosX(20);
            ImGui::PushFont(nullptr); // Use default font, but we'll make it bigger
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f)); // Red title
            ImGui::Text("SKECH [beta]");
            ImGui::PopStyleColor();
            ImGui::PopFont();

            ImGui::SetCursorPosY(60);

            // Navigation menu items exactly like Figma
            RenderSidebarItem("Player", 0, true);  // Player section (expanded)
            if (currentTab == 0) {
                RenderSidebarSubItem("Aimbot", 0);
                RenderSidebarSubItem("Player", 1);
            }

            RenderSidebarItem("Visuals", 1, false);
            RenderSidebarSubItem("Players", 2);
            RenderSidebarSubItem("World", 3);

            RenderSidebarItem("Miscellaneous", 2, false);
            RenderSidebarSubItem("Lists", 4);
            RenderSidebarSubItem("Miscellaneous", 5);
            RenderSidebarSubItem("Configs", 6);
        }
        ImGui::EndChild();

        // Main content area
        ImGui::SetCursorPos(ImVec2(sidebar_width + 20, 20));
        if (ImGui::BeginChild("MainContent", ImVec2(window_size.x - sidebar_width - 40, window_size.y - 40), false)) {

            // Render content based on current selection
            switch (currentTab) {
                case 0: RenderAimbotContent(); break;
                case 1: RenderPlayerContent(); break;
                case 2: RenderPlayersVisualContent(); break;
                case 3: RenderWorldContent(); break;
                case 4: RenderListsContent(); break;
                case 5: RenderMiscellaneousContent(); break;
                case 6: RenderConfigsContent(); break;
                default: RenderAimbotContent(); break;
            }
        }
        ImGui::EndChild();

        // Footer text exactly like Figma
        ImGui::SetCursorPos(ImVec2(sidebar_width / 2 - 60, window_size.y - 30));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.5f, 0.5f, 0.5f, 1.0f));
        ImGui::Text("SKECH [beta] for Fivem");
        ImGui::PopStyleColor();
    }
    ImGui::End();
}

void ImGuiMenu::RenderSidebarItem(const char* label, int index, bool expanded) {
    ImGui::SetCursorPosX(15);

    // Create selectable item with custom styling
    ImVec2 item_size = ImVec2(190, 35);
    ImVec2 cursor_pos = ImGui::GetCursorPos();

    bool is_selected = (currentTab == index);

    // Custom button styling for sidebar items
    if (is_selected) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(1.0f, 0.3f, 0.2f, 0.3f)); // Red background when selected
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));   // Red text when selected
    } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.0f, 0.0f, 0.0f)); // Transparent when not selected
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.7f, 0.7f, 0.7f, 1.0f));   // Gray text when not selected
    }

    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1.0f, 0.3f, 0.2f, 0.2f)); // Light red on hover
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(1.0f, 0.3f, 0.2f, 0.4f));  // Darker red when pressed

    if (ImGui::Button(label, item_size)) {
        currentTab = index;
    }

    ImGui::PopStyleColor(4);
    ImGui::Spacing();
}

void ImGuiMenu::RenderSidebarSubItem(const char* label, int index) {
    ImGui::SetCursorPosX(35); // Indent sub-items

    ImVec2 item_size = ImVec2(170, 30);
    bool is_selected = (currentTab == index);

    // Sub-item styling
    if (is_selected) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(1.0f, 0.3f, 0.2f, 0.2f));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));
    } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
    }

    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1.0f, 0.3f, 0.2f, 0.15f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(1.0f, 0.3f, 0.2f, 0.3f));

    // Add icon for sub-items (using simple text icons)
    std::string item_text = std::string("  ") + label; // Simple indentation

    if (ImGui::Button(item_text.c_str(), item_size)) {
        currentTab = index;
    }

    ImGui::PopStyleColor(4);
    ImGui::Spacing();
}

void ImGuiMenu::RenderAimbotContent() {
    // Title
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.9f, 0.9f, 0.9f, 1.0f));
    ImGui::SetCursorPosY(10);
    ImGui::Text("Aim");
    ImGui::PopStyleColor();

    ImGui::Spacing();
    ImGui::Spacing();

    // Create two columns layout exactly like Figma
    float content_width = ImGui::GetContentRegionAvail().x;
    float column_width = (content_width - 20) / 2; // 20px spacing between columns

    // Left Column - Aimbot Section
    if (ImGui::BeginChild("AimbotSection", ImVec2(column_width, 300), true, ImGuiWindowFlags_NoScrollbar)) {
        // Aimbot header with toggle
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f)); // Red circle
        ImGui::Text("●");
        ImGui::PopStyleColor();
        ImGui::SameLine();
        ImGui::Text("Aimbot");
        ImGui::SameLine();

        // Toggle switch (styled as in Figma)
        ImGui::SetCursorPosX(column_width - 60);
        DrawCustomToggle("##AimbotToggle", &aimbot.enabled);

        ImGui::Spacing();

        // Aimbot settings exactly like Figma
        DrawSettingRow("Aimbot Hotkey", "Mouse 2");
        DrawCheckboxRow("Target Peds", &aimbot.visibleOnly);
        DrawCheckboxRow("Visible Check", &aimbot.smoothAim);
        DrawSliderRow("Field Of View", &aimbot.fov, 1.0f, 180.0f);
        DrawSliderRow("Smooth", &aimbot.smoothness, 1.0f, 20.0f);
        DrawSliderRow("Curve", &aimbot.smoothness, 1.0f, 10.0f); // Using same var for demo
        DrawSliderRow("Aim Distance", &aimbot.fov, 50.0f, 1000.0f); // Using fov var for demo

        ImGui::Text("HitBox");
        DrawCheckboxRow("Dual Aimbot", &aimbot.visibleOnly);
        DrawCheckboxRow("FOV Circle", &aimbot.smoothAim);
    }
    ImGui::EndChild();

    ImGui::SameLine();
    ImGui::SetCursorPosX(column_width + 20);

    // Right Column - Silent Aim and Magic Bullet
    if (ImGui::BeginChild("RightColumn", ImVec2(column_width, 600), false, ImGuiWindowFlags_NoScrollbar)) {

        // Silent Aim Section
        if (ImGui::BeginChild("SilentAimSection", ImVec2(column_width, 180), true, ImGuiWindowFlags_NoScrollbar)) {
            // Silent Aim header
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));
            ImGui::Text("🎯");
            ImGui::PopStyleColor();
            ImGui::SameLine();
            ImGui::Text("Silent Aim");
            ImGui::SameLine();
            ImGui::SetCursorPosX(column_width - 60);
            DrawCustomToggle("##SilentAimToggle", &esp.enabled);

            ImGui::Spacing();

            DrawCheckboxRow("Enable Silent Aim", &esp.playerESP);
            DrawSettingRow("Silent Aim Hotkey", "Mouse 2");
            DrawSliderRow("Field Of View", &esp.maxDistance, 1.0f, 180.0f);
            DrawSettingRow("HitBox", "Head");
            DrawCheckboxRow("FOV Circle", &esp.healthBar);
        }
        ImGui::EndChild();

        ImGui::Spacing();

        // Magic Bullet Section
        if (ImGui::BeginChild("MagicBulletSection", ImVec2(column_width, 180), true, ImGuiWindowFlags_NoScrollbar)) {
            // Magic Bullet header
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));
            ImGui::Text("🔮");
            ImGui::PopStyleColor();
            ImGui::SameLine();
            ImGui::Text("Magic Bullet");
            ImGui::SameLine();
            ImGui::SetCursorPosX(column_width - 60);
            DrawCustomToggle("##MagicBulletToggle", &misc.godMode);

            ImGui::Spacing();

            DrawCheckboxRow("Enable Magic Bullet", &misc.infiniteAmmo);
            DrawSettingRow("Magic Bullet Hotkey", "Mouse 2");
            DrawSliderRow("Field Of View", &misc.speedMultiplier, 1.0f, 180.0f);
            DrawSettingRow("HitBox", "Head");
            DrawCheckboxRow("FOV Circle", &misc.noRecoil);
            DrawColorPickerRow("FOV Color", &esp.playerColor);
        }
        ImGui::EndChild();

        ImGui::Spacing();

        // Triggerbot Section (from Figma)
        if (ImGui::BeginChild("TriggerbotSection", ImVec2(column_width, 200), true, ImGuiWindowFlags_NoScrollbar)) {
            // Triggerbot header
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));
            ImGui::Text("🎯");
            ImGui::PopStyleColor();
            ImGui::SameLine();
            ImGui::Text("Triggerbot");
            ImGui::SameLine();
            ImGui::SetCursorPosX(column_width - 60);
            DrawCustomToggle("##TriggerbotToggle", &vehicle.godMode);

            ImGui::Spacing();

            DrawCheckboxRow("Use Hotkey", &vehicle.infiniteFuel);
            DrawSettingRow("Hotkey", "Mouse 2");
            DrawCheckboxRow("Target Players", &vehicle.noFallDamage);
            DrawCheckboxRow("Target Peds", &vehicle.instantRepair);
            DrawCheckboxRow("Target Dead", &misc.fastRun);
            DrawSliderRow("Delay", &vehicle.speedBoost, 0.0f, 1000.0f);
        }
        ImGui::EndChild();
    }
    ImGui::EndChild();
}

// Custom UI components to match Figma design exactly
void ImGuiMenu::DrawCustomToggle(const char* id, bool* value) {
    ImVec2 p = ImGui::GetCursorScreenPos();
    ImDrawList* draw_list = ImGui::GetWindowDrawList();

    float height = 20.0f;
    float width = 40.0f;
    float radius = height * 0.5f;

    // Create invisible button for interaction
    ImGui::InvisibleButton(id, ImVec2(width, height));
    if (ImGui::IsItemClicked()) {
        *value = !*value;
    }

    // Colors exactly like Figma toggle
    ImU32 bg_color = *value ? IM_COL32(255, 77, 51, 255) : IM_COL32(60, 60, 60, 255); // Red when on, gray when off
    ImU32 knob_color = IM_COL32(255, 255, 255, 255); // White knob

    // Draw toggle background
    draw_list->AddRectFilled(p, ImVec2(p.x + width, p.y + height), bg_color, radius);

    // Draw toggle knob
    float knob_pos_x = *value ? (p.x + width - radius) : (p.x + radius);
    draw_list->AddCircleFilled(ImVec2(knob_pos_x, p.y + radius), radius - 2.0f, knob_color);
}

void ImGuiMenu::DrawSettingRow(const char* label, const char* value) {
    ImGui::Text("%s", label);
    ImGui::SameLine();

    // Right-align the value
    float window_width = ImGui::GetWindowSize().x;
    float text_width = ImGui::CalcTextSize(value).x;
    ImGui::SetCursorPosX(window_width - text_width - 20);

    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f)); // Gray text for values
    ImGui::Text("%s", value);
    ImGui::PopStyleColor();
}

void ImGuiMenu::DrawCheckboxRow(const char* label, bool* value) {
    // Custom checkbox to match Figma style
    ImVec2 p = ImGui::GetCursorScreenPos();
    ImDrawList* draw_list = ImGui::GetWindowDrawList();

    float size = 16.0f;

    // Create invisible button for the checkbox
    ImGui::InvisibleButton(("##checkbox" + std::string(label)).c_str(), ImVec2(size, size));
    if (ImGui::IsItemClicked()) {
        *value = !*value;
    }

    // Draw checkbox
    ImU32 bg_color = *value ? IM_COL32(255, 77, 51, 255) : IM_COL32(60, 60, 60, 255);
    ImU32 border_color = IM_COL32(100, 100, 100, 255);

    draw_list->AddRectFilled(p, ImVec2(p.x + size, p.y + size), bg_color, 3.0f);
    draw_list->AddRect(p, ImVec2(p.x + size, p.y + size), border_color, 3.0f);

    // Draw checkmark if checked
    if (*value) {
        draw_list->AddLine(ImVec2(p.x + 4, p.y + 8), ImVec2(p.x + 7, p.y + 11), IM_COL32(255, 255, 255, 255), 2.0f);
        draw_list->AddLine(ImVec2(p.x + 7, p.y + 11), ImVec2(p.x + 12, p.y + 6), IM_COL32(255, 255, 255, 255), 2.0f);
    }

    // Label
    ImGui::SameLine();
    ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 5);
    ImGui::Text("%s", label);
}

void ImGuiMenu::DrawSliderRow(const char* label, float* value, float min, float max) {
    ImGui::Text("%s", label);

    // Custom slider styling to match Figma
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));        // Dark slider background
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.25f, 0.25f, 0.25f, 1.0f)); // Slightly lighter on hover
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));   // Even lighter when active
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(1.0f, 0.3f, 0.2f, 1.0f));      // Red slider grab
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(1.0f, 0.4f, 0.3f, 1.0f)); // Lighter red when dragging

    ImGui::SliderFloat(("##" + std::string(label)).c_str(), value, min, max, "%.1f");

    ImGui::PopStyleColor(5);
}

void ImGuiMenu::DrawColorPickerRow(const char* label, ImVec4* color) {
    ImGui::Text("%s", label);
    ImGui::SameLine();

    // Right-align color picker
    float window_width = ImGui::GetWindowSize().x;
    ImGui::SetCursorPosX(window_width - 50);

    // Small color button
    ImGui::ColorEdit4(("##" + std::string(label)).c_str(), (float*)color,
                     ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoLabel |
                     ImGuiColorEditFlags_AlphaPreview | ImGuiColorEditFlags_AlphaBar);
}

// Placeholder implementations for other menu sections
void ImGuiMenu::RenderPlayerContent() {
    ImGui::Text("Player Settings");
    ImGui::Separator();
    ImGui::Text("Player-specific features will be implemented here.");
}

void ImGuiMenu::RenderPlayersVisualContent() {
    ImGui::Text("Players Visual (ESP)");
    ImGui::Separator();
    ImGui::Text("ESP and visual features for players.");
}

void ImGuiMenu::RenderWorldContent() {
    ImGui::Text("World Visuals");
    ImGui::Separator();
    ImGui::Text("World ESP and visual features.");
}

void ImGuiMenu::RenderListsContent() {
    ImGui::Text("Lists");
    ImGui::Separator();
    ImGui::Text("Player lists and management features.");
}

void ImGuiMenu::RenderMiscellaneousContent() {
    ImGui::Text("Miscellaneous");
    ImGui::Separator();
    ImGui::Text("Various cheat features and utilities.");
}

void ImGuiMenu::RenderConfigsContent() {
    ImGui::Text("Configurations");
    ImGui::Separator();
    ImGui::Text("Save and load cheat configurations.");
}

// DirectX implementation
bool ImGuiMenu::CreateDeviceD3D(HWND hWnd) {
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    HRESULT res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &pSwapChain, &pd3dDevice, &featureLevel, &pd3dDeviceContext);
    if (res == DXGI_ERROR_UNSUPPORTED) // Try high-performance WARP software driver if hardware is not available.
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_WARP, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &pSwapChain, &pd3dDevice, &featureLevel, &pd3dDeviceContext);
    if (res != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void ImGuiMenu::CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (pSwapChain) { pSwapChain->Release(); pSwapChain = nullptr; }
    if (pd3dDeviceContext) { pd3dDeviceContext->Release(); pd3dDeviceContext = nullptr; }
    if (pd3dDevice) { pd3dDevice->Release(); pd3dDevice = nullptr; }
}

void ImGuiMenu::CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &pMainRenderTargetView);
    pBackBuffer->Release();
}

void ImGuiMenu::CleanupRenderTarget() {
    if (pMainRenderTargetView) { pMainRenderTargetView->Release(); pMainRenderTargetView = nullptr; }
}

// Window procedure
LRESULT WINAPI ImGuiMenu::WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg) {
    case WM_SIZE:
        if (g_Menu && g_Menu->pd3dDevice != nullptr && wParam != SIZE_MINIMIZED) {
            g_Menu->CleanupRenderTarget();
            g_Menu->pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            g_Menu->CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        ::PostQuitMessage(0);
        return 0;
    }
    return ::DefWindowProcW(hWnd, msg, wParam, lParam);
}

void ImGuiMenu::HandleInput() {
    // Handle menu toggle (INSERT key)
    if (GetAsyncKeyState(VK_INSERT) & 1) {
        Toggle();
    }
}
