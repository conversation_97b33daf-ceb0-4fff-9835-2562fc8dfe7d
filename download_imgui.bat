@echo off
echo ================================
echo Baixando ImGui para o Menu
echo ================================

REM Check if curl is available
where curl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] curl não encontrado. Tentando usar PowerShell...
    goto :use_powershell
)

REM Create vendor directory
if not exist "vendor" mkdir vendor
if not exist "vendor\imgui" mkdir vendor\imgui

echo [~] Baixando ImGui v1.90.4...

REM Download ImGui files using curl
curl -L -o "vendor\imgui\imgui.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui.h"
curl -L -o "vendor\imgui\imgui.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui.cpp"
curl -L -o "vendor\imgui\imgui_demo.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_demo.cpp"
curl -L -o "vendor\imgui\imgui_draw.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_draw.cpp"
curl -L -o "vendor\imgui\imgui_tables.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_tables.cpp"
curl -L -o "vendor\imgui\imgui_widgets.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_widgets.cpp"
curl -L -o "vendor\imgui\imgui_impl_win32.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_win32.h"
curl -L -o "vendor\imgui\imgui_impl_win32.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_win32.cpp"
curl -L -o "vendor\imgui\imgui_impl_dx11.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_dx11.h"
curl -L -o "vendor\imgui\imgui_impl_dx11.cpp" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_dx11.cpp"
curl -L -o "vendor\imgui\imconfig.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imconfig.h"
curl -L -o "vendor\imgui\imgui_internal.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_internal.h"
curl -L -o "vendor\imgui\imstb_rectpack.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_rectpack.h"
curl -L -o "vendor\imgui\imstb_textedit.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_textedit.h"
curl -L -o "vendor\imgui\imstb_truetype.h" "https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_truetype.h"

goto :check_files

:use_powershell
echo [~] Usando PowerShell para baixar ImGui...

REM Create vendor directory
if not exist "vendor" mkdir vendor
if not exist "vendor\imgui" mkdir vendor\imgui

REM Download using PowerShell
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui.h' -OutFile 'vendor\imgui\imgui.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui.cpp' -OutFile 'vendor\imgui\imgui.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_demo.cpp' -OutFile 'vendor\imgui\imgui_demo.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_draw.cpp' -OutFile 'vendor\imgui\imgui_draw.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_tables.cpp' -OutFile 'vendor\imgui\imgui_tables.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_widgets.cpp' -OutFile 'vendor\imgui\imgui_widgets.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_win32.h' -OutFile 'vendor\imgui\imgui_impl_win32.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_win32.cpp' -OutFile 'vendor\imgui\imgui_impl_win32.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_dx11.h' -OutFile 'vendor\imgui\imgui_impl_dx11.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/backends/imgui_impl_dx11.cpp' -OutFile 'vendor\imgui\imgui_impl_dx11.cpp'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imconfig.h' -OutFile 'vendor\imgui\imconfig.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imgui_internal.h' -OutFile 'vendor\imgui\imgui_internal.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_rectpack.h' -OutFile 'vendor\imgui\imstb_rectpack.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_textedit.h' -OutFile 'vendor\imgui\imstb_textedit.h'"
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/ocornut/imgui/v1.90.4/imstb_truetype.h' -OutFile 'vendor\imgui\imstb_truetype.h'"

:check_files
echo [~] Verificando arquivos baixados...

if exist "vendor\imgui\imgui.h" (
    echo [+] ImGui baixado com sucesso!
    echo [+] Arquivos salvos em: vendor\imgui\
    echo.
    echo ================================
    echo DOWNLOAD CONCLUÍDO!
    echo ================================
    echo.
    echo Agora você pode executar: build_menu.bat
) else (
    echo [-] Falha no download do ImGui
    echo [!] Tente baixar manualmente de: https://github.com/ocornut/imgui
)

pause
