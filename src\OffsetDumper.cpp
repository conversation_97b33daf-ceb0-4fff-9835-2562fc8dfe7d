#include "OffsetDumper.hpp"
#include "ProcessManager.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <algorithm>

// JSON library for config parsing
#include <nlohmann/json.hpp>
using json = nlohmann::json;

namespace FiveMCheat {

    OffsetDumper::OffsetDumper() 
        : m_totalSignatures(0)
        , m_foundSignatures(0)
        , m_failedSignatures(0) {
        
        m_processManager = std::make_shared<ProcessManager>();
        
        // Set default config
        m_config.gameName = "FiveM";
        m_config.gameExecutable = "FiveM.exe";
        m_config.outputDirectory = "./dumps/";
        m_config.headerFileName = "offsets.hpp";
        m_config.generateHeader = true;
        m_config.generateCheatTable = false;
        m_config.generateReClassFile = false;
        m_config.relativeByDefault = true;
        m_config.fileOnly = false;
        m_config.verboseOutput = true;
        m_config.maxRetries = 3;
        m_config.retryDelay = 1000;
    }

    OffsetDumper::~OffsetDumper() {
        // Cleanup
    }

    bool OffsetDumper::LoadConfig(const std::string& configFile) {
        try {
            std::ifstream file(configFile);
            if (!file.is_open()) {
                SetError("Failed to open config file: " + configFile);
                return false;
            }

            json configJson;
            file >> configJson;

            // Parse basic settings
            if (configJson.contains("gameName")) {
                m_config.gameName = configJson["gameName"];
            }
            if (configJson.contains("gameExecutable")) {
                m_config.gameExecutable = configJson["gameExecutable"];
            }
            if (configJson.contains("outputDirectory")) {
                m_config.outputDirectory = configJson["outputDirectory"];
            }
            if (configJson.contains("headerFileName")) {
                m_config.headerFileName = configJson["headerFileName"];
            }
            if (configJson.contains("generateHeader")) {
                m_config.generateHeader = configJson["generateHeader"];
            }
            if (configJson.contains("generateCheatTable")) {
                m_config.generateCheatTable = configJson["generateCheatTable"];
            }
            if (configJson.contains("generateReClassFile")) {
                m_config.generateReClassFile = configJson["generateReClassFile"];
            }
            if (configJson.contains("relativeByDefault")) {
                m_config.relativeByDefault = configJson["relativeByDefault"];
            }
            if (configJson.contains("fileOnly")) {
                m_config.fileOnly = configJson["fileOnly"];
            }
            if (configJson.contains("diskFilePath")) {
                m_config.diskFilePath = configJson["diskFilePath"];
            }
            if (configJson.contains("verboseOutput")) {
                m_config.verboseOutput = configJson["verboseOutput"];
            }
            if (configJson.contains("maxRetries")) {
                m_config.maxRetries = configJson["maxRetries"];
            }
            if (configJson.contains("retryDelay")) {
                m_config.retryDelay = configJson["retryDelay"];
            }

            // Parse additional modules
            if (configJson.contains("additionalModules")) {
                m_config.additionalModules.clear();
                for (const auto& module : configJson["additionalModules"]) {
                    m_config.additionalModules.push_back(module);
                }
            }

            // Parse signatures
            if (configJson.contains("signatures")) {
                m_config.signatures.clear();
                for (const auto& sigJson : configJson["signatures"]) {
                    Signature sig;
                    sig.name = sigJson["name"];
                    sig.pattern = sigJson["pattern"];
                    
                    if (sigJson.contains("mask")) {
                        sig.mask = sigJson["mask"];
                    } else {
                        // Generate mask from pattern
                        std::istringstream iss(sig.pattern);
                        std::string byte;
                        sig.mask = "";
                        while (iss >> byte) {
                            sig.mask += (byte == "?") ? "?" : "x";
                        }
                    }
                    
                    sig.module = sigJson.value("module", "");
                    sig.isRelative = sigJson.value("isRelative", m_config.relativeByDefault);
                    sig.operandLocation = sigJson.value("operandLocation", 1);
                    sig.operandLength = sigJson.value("operandLength", 4);
                    sig.additionalOffset = sigJson.value("additionalOffset", 0);
                    sig.description = sigJson.value("description", "");
                    sig.required = sigJson.value("required", true);
                    
                    m_config.signatures.push_back(sig);
                }
            }

            LogMessage("Config loaded successfully: " + configFile);
            return true;

        } catch (const std::exception& e) {
            SetError("Failed to parse config file: " + std::string(e.what()));
            return false;
        }
    }

    bool OffsetDumper::SaveConfig(const std::string& configFile) {
        try {
            json configJson;
            
            // Basic settings
            configJson["gameName"] = m_config.gameName;
            configJson["gameExecutable"] = m_config.gameExecutable;
            configJson["outputDirectory"] = m_config.outputDirectory;
            configJson["headerFileName"] = m_config.headerFileName;
            configJson["generateHeader"] = m_config.generateHeader;
            configJson["generateCheatTable"] = m_config.generateCheatTable;
            configJson["generateReClassFile"] = m_config.generateReClassFile;
            configJson["relativeByDefault"] = m_config.relativeByDefault;
            configJson["fileOnly"] = m_config.fileOnly;
            configJson["diskFilePath"] = m_config.diskFilePath;
            configJson["verboseOutput"] = m_config.verboseOutput;
            configJson["maxRetries"] = m_config.maxRetries;
            configJson["retryDelay"] = m_config.retryDelay;
            
            // Additional modules
            configJson["additionalModules"] = m_config.additionalModules;
            
            // Signatures
            json signaturesJson = json::array();
            for (const auto& sig : m_config.signatures) {
                json sigJson;
                sigJson["name"] = sig.name;
                sigJson["pattern"] = sig.pattern;
                sigJson["mask"] = sig.mask;
                sigJson["module"] = sig.module;
                sigJson["isRelative"] = sig.isRelative;
                sigJson["operandLocation"] = sig.operandLocation;
                sigJson["operandLength"] = sig.operandLength;
                sigJson["additionalOffset"] = sig.additionalOffset;
                sigJson["description"] = sig.description;
                sigJson["required"] = sig.required;
                signaturesJson.push_back(sigJson);
            }
            configJson["signatures"] = signaturesJson;
            
            // Write to file
            std::ofstream file(configFile);
            if (!file.is_open()) {
                SetError("Failed to create config file: " + configFile);
                return false;
            }
            
            file << configJson.dump(2);
            file.close();
            
            LogMessage("Config saved successfully: " + configFile);
            return true;
            
        } catch (const std::exception& e) {
            SetError("Failed to save config file: " + std::string(e.what()));
            return false;
        }
    }

    bool OffsetDumper::DumpOffsets() {
        LogMessage("Starting offset dump for " + m_config.gameName);
        m_startTime = std::chrono::steady_clock::now();
        
        // Clear previous results
        m_dumpedOffsets.clear();
        m_modules.clear();
        m_totalSignatures = static_cast<int>(m_config.signatures.size());
        m_foundSignatures = 0;
        m_failedSignatures = 0;
        
        // Validate config
        if (!ValidateConfig()) {
            return false;
        }
        
        bool success = false;
        
        if (m_config.fileOnly) {
            success = DumpFromDisk();
        } else {
            success = DumpFromRuntime();
        }
        
        m_endTime = std::chrono::steady_clock::now();
        
        if (success) {
            LogMessage("Offset dump completed successfully");
            PrintStatistics();
            
            if (!GenerateOutputs()) {
                LogError("Failed to generate output files");
                return false;
            }
        } else {
            LogError("Offset dump failed");
        }
        
        return success;
    }

    bool OffsetDumper::DumpFromRuntime() {
        LogMessage("Dumping from runtime process");
        
        // Initialize process
        if (!InitializeProcess()) {
            return false;
        }
        
        // Load modules
        if (!LoadModules()) {
            return false;
        }
        
        // Process signatures
        if (!ProcessSignatures()) {
            return false;
        }
        
        return true;
    }

    bool OffsetDumper::DumpFromDisk() {
        LogMessage("Dumping from disk file: " + m_config.diskFilePath);
        
        // Read file to memory
        std::vector<uint8_t> fileData;
        if (!ReadFileToMemory(m_config.diskFilePath, fileData)) {
            SetError("Failed to read file: " + m_config.diskFilePath);
            return false;
        }
        
        LogMessage("File loaded, size: " + std::to_string(fileData.size()) + " bytes");
        
        // Process signatures in file data
        for (const auto& signature : m_config.signatures) {
            if (m_config.verboseOutput) {
                LogMessage("Processing signature: " + signature.name);
            }
            
            uintptr_t address = FindPatternInMemory(fileData, signature.pattern, signature.mask);
            
            DumpedOffset offset;
            offset.name = signature.name;
            offset.address = address;
            offset.rva = address; // For disk dumps, address is already RVA
            offset.module = "Main";
            offset.found = (address != 0);
            offset.description = signature.description;
            offset.timestamp = std::chrono::system_clock::now();
            
            if (offset.found) {
                m_foundSignatures++;
                if (m_config.verboseOutput) {
                    LogMessage("Found " + signature.name + " at RVA: 0x" + AddressToString(address));
                }
            } else {
                m_failedSignatures++;
                if (signature.required) {
                    LogError("Required signature not found: " + signature.name);
                }
            }
            
            m_dumpedOffsets.push_back(offset);
        }
        
        return true;
    }

    bool OffsetDumper::InitializeProcess() {
        LogMessage("Initializing process: " + m_config.gameExecutable);
        
        // Try to attach to process
        for (int retry = 0; retry < m_config.maxRetries; ++retry) {
            if (m_processManager->AttachToProcess(m_config.gameExecutable)) {
                LogMessage("Successfully attached to process");
                return true;
            }
            
            if (retry < m_config.maxRetries - 1) {
                LogMessage("Retrying in " + std::to_string(m_config.retryDelay) + "ms...");
                Sleep(m_config.retryDelay);
            }
        }
        
        SetError("Failed to attach to process: " + m_config.gameExecutable);
        return false;
    }

    bool OffsetDumper::LoadModules() {
        LogMessage("Loading modules");
        
        // Add main module
        ModuleInfo mainModule;
        mainModule.name = m_config.gameExecutable;
        mainModule.baseAddress = m_processManager->GetBaseAddress();
        mainModule.size = m_processManager->GetProcessInfo().moduleSize;
        mainModule.loaded = true;
        m_modules.push_back(mainModule);
        
        // Add additional modules
        for (const auto& moduleName : m_config.additionalModules) {
            ModuleInfo module;
            module.name = moduleName;
            module.baseAddress = m_processManager->GetModuleBaseAddress(moduleName);
            module.size = m_processManager->GetModuleSize(moduleName);
            module.loaded = (module.baseAddress != 0);
            
            if (module.loaded) {
                LogMessage("Loaded module: " + moduleName + " at 0x" + AddressToString(module.baseAddress));
            } else {
                LogWarning("Failed to load module: " + moduleName);
            }
            
            m_modules.push_back(module);
        }
        
        return true;
    }

    bool OffsetDumper::ProcessSignatures() {
        LogMessage("Processing " + std::to_string(m_config.signatures.size()) + " signatures");
        
        for (const auto& signature : m_config.signatures) {
            if (!ProcessSignature(signature)) {
                if (signature.required) {
                    LogError("Failed to process required signature: " + signature.name);
                    return false;
                }
            }
        }
        
        return true;
    }
