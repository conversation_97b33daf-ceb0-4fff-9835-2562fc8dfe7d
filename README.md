# FiveM External Menu

Um menu externo profissional para FiveM desenvolvido em C++ com interface moderna usando Dear ImGui.

## 🚀 Características

- **Interface Moderna**: Design dark profissional com Dear ImGui
- **Múltiplas Funcionalidades**: 
  - Controle completo do jogador (saúde, armadura, dinheiro)
  - Spawn e modificação de veículos
  - Sistema de teleporte avançado
  - Recursos visuais (ESP, aimbot)
  - Sistema de configuração personalizável
- **Comunicação Segura**: Conexão via named pipes com o FiveM
- **Hotkeys Customizáveis**: Configure suas próprias teclas de atalho
- **Profiles**: Salve e carregue diferentes configurações

## 📋 Pré-requisitos

- Windows 10/11
- Visual Studio 2022 (ou Build Tools)
- CMake 3.16+
- Git
- GLFW3 (pode ser instalado via vcpkg)

## 🛠️ Instalação

### 1. Clonar o repositório
```bash
git clone <seu-repositorio>
cd cheat-externo-fivem
```

### 2. Configurar dependências
```bash
setup_dependencies.bat
```

### 3. Instalar GLFW3 (usando vcpkg - recomendado)
```bash
vcpkg install glfw3:x64-windows
```

### 4. Compilar o projeto
```bash
build.bat
```

## 🎮 Como Usar

1. **Iniciar o FiveM** e entrar em um servidor
2. **Executar o menu**: `build/Release/FiveMExternalMenu.exe`
3. **Abrir interface**: Pressione `F1` (padrão)
4. **Navegar**: Use as abas para acessar diferentes funcionalidades

### Controles Padrão
- `F1`: Abrir/Fechar menu principal
- `F2`: Toggle janela do jogador
- `F3`: Toggle janela de veículos
- `F4`: Toggle janela de teleporte
- `F5`: Toggle janela visual

## 📁 Estrutura do Projeto

```
├── include/                 # Headers (.hpp)
│   ├── windows/            # Janelas específicas
│   ├── MenuManager.hpp     # Gerenciador principal
│   ├── Window.hpp          # Classe base das janelas
│   ├── FiveMCommunicator.hpp # Comunicação com FiveM
│   └── Config.hpp          # Sistema de configuração
├── src/                    # Implementações (.cpp)
│   ├── windows/           # Implementações das janelas
│   ├── main.cpp           # Ponto de entrada
│   ├── MenuManager.cpp    # Implementação do gerenciador
│   └── Window.cpp         # Implementação da classe base
├── vendor/                # Dependências externas
│   ├── imgui/            # Dear ImGui
│   └── glew/             # GLEW
├── resources/            # Recursos (fontes, configs)
│   ├── config.json       # Configuração padrão
│   └── fonts/           # Fontes personalizadas
└── CMakeLists.txt       # Configuração do CMake
```

## ⚙️ Configuração

O arquivo `resources/config.json` contém todas as configurações:

```json
{
  "general": {
    "theme": "dark",
    "language": "pt-BR",
    "auto_connect": true
  },
  "hotkeys": [
    {
      "action": "toggle_menu",
      "key": 290,
      "ctrl": false,
      "shift": false,
      "alt": false
    }
  ],
  "player": {
    "godMode": false,
    "invisible": false,
    "noClip": false
  }
}
```

## 🔧 Funcionalidades

### Jogador
- Controle de saúde e armadura
- Configurações de movimento (velocidade, stamina infinita)
- Gerenciamento de dinheiro
- Sistema de armas completo
- Modos especiais (Deus, Invisível, NoClip)

### Veículos
- Spawn de veículos por categoria
- Modificações em tempo real
- Sistema de favoritos
- Configurações de performance
- Customização visual

### Teleporte
- Locais predefinidos organizados por categoria
- Coordenadas customizadas
- Teleporte para waypoint
- Histórico de teleportes
- Sistema de favoritos

### Visual
- ESP para jogadores, veículos e objetos
- Sistema de aimbot configurável
- Modificações do mundo (clima, tempo)
- Efeitos visuais especiais
- Câmera livre

## 🛡️ Segurança

- **Comunicação Criptografada**: Todos os comandos são enviados de forma segura
- **Detecção Anti-Cheat**: Implementa técnicas para evitar detecção
- **Logs Mínimos**: Não deixa rastros desnecessários no sistema

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto é apenas para fins educacionais. Use por sua própria conta e risco.

## ⚠️ Aviso Legal

Este software é fornecido "como está" sem garantias. O uso em servidores públicos pode resultar em banimento. Use apenas em servidores privados ou para fins de teste.

## 📞 Suporte

Para suporte e dúvidas, abra uma issue no repositório.
