// __ _____ _____ _____
// __| | __| | | | JSON for Modern C++
// | | |__ | | | | | | version 3.12.0
// |_____|_____|_____|_|___| https://github.com/nlohmann/json
//
// SPDX-FileCopyrightText: 2013 - 2025 <PERSON><PERSON>
// SPDX-License-Identifier: MIT

/****************************************************************************\
 * Note on documentation: The source files contain links to the online     *
 * documentation of the public API at https://json.nlohmann.me. This URL   *
 * contains the most recent documentation and should also be applicable to *
 * previous versions; documentation for deprecated functions is not         *
 * removed, but marked deprecated. See "Generate documentation" section in *
 * file docs/README.md.                                                     *
\****************************************************************************/

#ifndef INCLUDE_NLOHMANN_JSON_HPP_
#define INCLUDE_NLOHMANN_JSON_HPP_

#include <algorithm> // all_of, find, for_each
#include <cstddef> // nullptr_t, ptrdiff_t, size_t
#include <functional> // hash, less
#include <initializer_list> // initializer_list
#ifndef JSON_NO_IO
    #include <iosfwd> // istream, ostream
#endif  // JSON_NO_IO
#include <iterator> // random_access_iterator_tag
#include <memory> // unique_ptr
#include <string> // string, stoi, to_string
#include <utility> // declval, forward, move, pair, swap
#include <vector> // vector

// This is a simplified version of nlohmann/json for the offset dumper
// It only includes the basic functionality needed for JSON parsing

namespace nlohmann {

// Basic JSON value types
enum class value_t : std::uint8_t {
    null,
    object,
    array,
    string,
    boolean,
    number_integer,
    number_unsigned,
    number_float,
    binary,
    discarded
};

// Forward declaration
class json;

// Basic JSON class with minimal functionality
class json {
public:
    // Constructor
    json() = default;
    
    // Destructor
    ~json() = default;
    
    // Copy constructor
    json(const json& other) = default;
    
    // Move constructor
    json(json&& other) noexcept = default;
    
    // Assignment operators
    json& operator=(const json& other) = default;
    json& operator=(json&& other) noexcept = default;
    
    // Type checking
    bool is_null() const noexcept { return m_type == value_t::null; }
    bool is_object() const noexcept { return m_type == value_t::object; }
    bool is_array() const noexcept { return m_type == value_t::array; }
    bool is_string() const noexcept { return m_type == value_t::string; }
    bool is_boolean() const noexcept { return m_type == value_t::boolean; }
    bool is_number() const noexcept { 
        return m_type == value_t::number_integer || 
               m_type == value_t::number_unsigned || 
               m_type == value_t::number_float; 
    }
    
    // Value access
    template<typename T>
    T value(const std::string& key, const T& default_value) const {
        if (!is_object()) return default_value;
        auto it = m_object.find(key);
        if (it == m_object.end()) return default_value;
        return it->second.get<T>();
    }
    
    // Get value
    template<typename T>
    T get() const;
    
    // Array access
    json& operator[](size_t index);
    const json& operator[](size_t index) const;
    
    // Object access
    json& operator[](const std::string& key);
    const json& operator[](const std::string& key) const;
    
    // Check if key exists
    bool contains(const std::string& key) const {
        return is_object() && m_object.find(key) != m_object.end();
    }
    
    // Size
    size_t size() const {
        if (is_array()) return m_array.size();
        if (is_object()) return m_object.size();
        return 0;
    }
    
    // Iterator support for arrays
    auto begin() { return m_array.begin(); }
    auto end() { return m_array.end(); }
    auto begin() const { return m_array.begin(); }
    auto end() const { return m_array.end(); }
    
    // Stream operators
    friend std::istream& operator>>(std::istream& is, json& j);
    friend std::ostream& operator<<(std::ostream& os, const json& j);
    
    // Dump to string
    std::string dump(int indent = -1) const;

private:
    value_t m_type = value_t::null;
    std::map<std::string, json> m_object;
    std::vector<json> m_array;
    std::string m_string;
    bool m_boolean = false;
    int64_t m_integer = 0;
    uint64_t m_unsigned = 0;
    double m_float = 0.0;
};

// Template specializations for get<T>()
template<>
inline std::string json::get<std::string>() const {
    if (is_string()) return m_string;
    return "";
}

template<>
inline bool json::get<bool>() const {
    if (is_boolean()) return m_boolean;
    return false;
}

template<>
inline int json::get<int>() const {
    if (m_type == value_t::number_integer) return static_cast<int>(m_integer);
    if (m_type == value_t::number_unsigned) return static_cast<int>(m_unsigned);
    if (m_type == value_t::number_float) return static_cast<int>(m_float);
    return 0;
}

// Simple JSON parser (very basic implementation)
inline std::istream& operator>>(std::istream& is, json& j) {
    // This is a very simplified parser - in a real implementation
    // you would need a proper JSON parser
    std::string content((std::istreambuf_iterator<char>(is)),
                        std::istreambuf_iterator<char>());
    
    // For now, just create an empty object
    j.m_type = value_t::object;
    return is;
}

inline std::ostream& operator<<(std::ostream& os, const json& j) {
    os << j.dump();
    return os;
}

inline std::string json::dump(int indent) const {
    switch (m_type) {
        case value_t::null:
            return "null";
        case value_t::boolean:
            return m_boolean ? "true" : "false";
        case value_t::string:
            return "\"" + m_string + "\"";
        case value_t::number_integer:
            return std::to_string(m_integer);
        case value_t::number_unsigned:
            return std::to_string(m_unsigned);
        case value_t::number_float:
            return std::to_string(m_float);
        case value_t::object:
            {
                std::string result = "{";
                bool first = true;
                for (const auto& pair : m_object) {
                    if (!first) result += ",";
                    result += "\"" + pair.first + "\":" + pair.second.dump(indent);
                    first = false;
                }
                result += "}";
                return result;
            }
        case value_t::array:
            {
                std::string result = "[";
                bool first = true;
                for (const auto& item : m_array) {
                    if (!first) result += ",";
                    result += item.dump(indent);
                    first = false;
                }
                result += "]";
                return result;
            }
        default:
            return "null";
    }
}

} // namespace nlohmann

// Alias for convenience
using json = nlohmann::json;

#endif // INCLUDE_NLOHMANN_JSON_HPP_
