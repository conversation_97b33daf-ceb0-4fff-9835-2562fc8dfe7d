#include "MemoryScanner.hpp"
#include "ProcessManager.hpp"
#include "GameStructures.hpp"
#include <iostream>
#include <algorithm>
#include <sstream>

namespace FiveMCheat {

    MemoryScanner::MemoryScanner(std::shared_ptr<ProcessManager> processManager)
        : m_processManager(processManager)
        , m_playerManager(0)
        , m_localPlayer(0)
        , m_entityList(0)
        , m_vehicleManager(0)
        , m_worldPtr(0)
        , m_viewMatrix(0)
        , m_cameraManager(0) {
        
        InitializeFiveMPatterns();
    }

    MemoryScanner::~MemoryScanner() {
        // Cleanup if needed
    }

    uintptr_t MemoryScanner::FindPattern(const std::string& pattern, const std::string& mask, 
                                        uintptr_t startAddress, size_t scanSize) {
        
        if (!m_processManager->IsAttached()) {
            return 0;
        }

        std::vector<uint8_t> patternBytes = StringToBytes(pattern);
        return FindPattern(patternBytes, mask, startAddress, scanSize);
    }

    uintptr_t MemoryScanner::FindPattern(const std::vector<uint8_t>& pattern, const std::string& mask,
                                        uintptr_t startAddress, size_t scanSize) {
        
        if (!m_processManager->IsAttached() || pattern.empty()) {
            return 0;
        }

        // If no start address specified, use module base
        if (startAddress == 0) {
            startAddress = m_processManager->GetBaseAddress();
        }

        // If no scan size specified, use module size
        if (scanSize == 0) {
            scanSize = m_processManager->GetProcessInfo().moduleSize;
        }

        // Read memory in chunks to avoid large allocations
        const size_t chunkSize = 0x1000; // 4KB chunks
        std::vector<uint8_t> buffer(chunkSize);

        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = std::min(chunkSize, scanSize - offset);
            uintptr_t currentAddress = startAddress + offset;

            if (m_processManager->ReadMemory(currentAddress, buffer.data(), currentChunkSize)) {
                // Search for pattern in current chunk
                for (size_t i = 0; i <= currentChunkSize - pattern.size(); ++i) {
                    if (ComparePattern(buffer.data() + i, pattern, mask)) {
                        return currentAddress + i;
                    }
                }
            }
        }

        return 0;
    }

    uintptr_t MemoryScanner::FindSignature(const Signature& signature) {
        uintptr_t moduleBase = 0;
        
        if (!signature.module.empty()) {
            moduleBase = m_processManager->GetModuleBaseAddress(signature.module);
            if (moduleBase == 0) {
                std::cout << "[!] Module not found: " << signature.module << std::endl;
                return 0;
            }
        } else {
            moduleBase = m_processManager->GetBaseAddress();
        }

        size_t moduleSize = m_processManager->GetModuleSize(signature.module.empty() ? 
                                                           m_processManager->GetProcessInfo().processName : 
                                                           signature.module);

        uintptr_t result = FindPattern(signature.pattern, signature.mask, moduleBase, moduleSize);
        
        if (result != 0 && signature.offset != 0) {
            result += signature.offset;
        }

        return result;
    }

    std::vector<uintptr_t> MemoryScanner::FindAllPatterns(const std::string& pattern, const std::string& mask) {
        std::vector<uintptr_t> results;
        
        if (!m_processManager->IsAttached()) {
            return results;
        }

        std::vector<uint8_t> patternBytes = StringToBytes(pattern);
        uintptr_t startAddress = m_processManager->GetBaseAddress();
        size_t scanSize = m_processManager->GetProcessInfo().moduleSize;

        const size_t chunkSize = 0x1000;
        std::vector<uint8_t> buffer(chunkSize);

        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = std::min(chunkSize, scanSize - offset);
            uintptr_t currentAddress = startAddress + offset;

            if (m_processManager->ReadMemory(currentAddress, buffer.data(), currentChunkSize)) {
                for (size_t i = 0; i <= currentChunkSize - patternBytes.size(); ++i) {
                    if (ComparePattern(buffer.data() + i, patternBytes, mask)) {
                        results.push_back(currentAddress + i);
                    }
                }
            }
        }

        return results;
    }

    uintptr_t MemoryScanner::ScanModule(const std::string& moduleName, const std::string& pattern, const std::string& mask) {
        uintptr_t moduleBase = m_processManager->GetModuleBaseAddress(moduleName);
        if (moduleBase == 0) {
            return 0;
        }

        size_t moduleSize = m_processManager->GetModuleSize(moduleName);
        return FindPattern(pattern, mask, moduleBase, moduleSize);
    }

    uintptr_t MemoryScanner::ScanRegion(uintptr_t startAddress, size_t size, const std::string& pattern, const std::string& mask) {
        return FindPattern(pattern, mask, startAddress, size);
    }

    uintptr_t MemoryScanner::FindString(const std::string& str, bool caseSensitive) {
        if (!m_processManager->IsAttached() || str.empty()) {
            return 0;
        }

        std::string searchStr = caseSensitive ? str : str;
        if (!caseSensitive) {
            std::transform(searchStr.begin(), searchStr.end(), searchStr.begin(), ::tolower);
        }

        uintptr_t startAddress = m_processManager->GetBaseAddress();
        size_t scanSize = m_processManager->GetProcessInfo().moduleSize;

        const size_t chunkSize = 0x1000;
        std::vector<uint8_t> buffer(chunkSize);

        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = std::min(chunkSize, scanSize - offset);
            uintptr_t currentAddress = startAddress + offset;

            if (m_processManager->ReadMemory(currentAddress, buffer.data(), currentChunkSize)) {
                std::string bufferStr(reinterpret_cast<char*>(buffer.data()), currentChunkSize);
                
                if (!caseSensitive) {
                    std::transform(bufferStr.begin(), bufferStr.end(), bufferStr.begin(), ::tolower);
                }

                size_t pos = bufferStr.find(searchStr);
                if (pos != std::string::npos) {
                    return currentAddress + pos;
                }
            }
        }

        return 0;
    }

    uintptr_t MemoryScanner::FindStringW(const std::wstring& str, bool caseSensitive) {
        if (!m_processManager->IsAttached() || str.empty()) {
            return 0;
        }

        std::wstring searchStr = str;
        if (!caseSensitive) {
            std::transform(searchStr.begin(), searchStr.end(), searchStr.begin(), ::towlower);
        }

        uintptr_t startAddress = m_processManager->GetBaseAddress();
        size_t scanSize = m_processManager->GetProcessInfo().moduleSize;

        const size_t chunkSize = 0x1000;
        std::vector<uint8_t> buffer(chunkSize);

        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = std::min(chunkSize, scanSize - offset);
            uintptr_t currentAddress = startAddress + offset;

            if (m_processManager->ReadMemory(currentAddress, buffer.data(), currentChunkSize)) {
                std::wstring bufferStr(reinterpret_cast<wchar_t*>(buffer.data()), currentChunkSize / sizeof(wchar_t));
                
                if (!caseSensitive) {
                    std::transform(bufferStr.begin(), bufferStr.end(), bufferStr.begin(), ::towlower);
                }

                size_t pos = bufferStr.find(searchStr);
                if (pos != std::wstring::npos) {
                    return currentAddress + (pos * sizeof(wchar_t));
                }
            }
        }

        return 0;
    }

    uintptr_t MemoryScanner::FindReference(uintptr_t address) {
        if (!m_processManager->IsAttached()) {
            return 0;
        }

        // Convert address to bytes for pattern matching
        std::vector<uint8_t> addressBytes(sizeof(uintptr_t));
        memcpy(addressBytes.data(), &address, sizeof(uintptr_t));

        std::string mask(sizeof(uintptr_t), 'x');
        return FindPattern(addressBytes, mask);
    }

    std::vector<uintptr_t> MemoryScanner::FindReferences(uintptr_t address) {
        std::vector<uintptr_t> references;
        
        if (!m_processManager->IsAttached()) {
            return references;
        }

        // Convert address to bytes for pattern matching
        std::vector<uint8_t> addressBytes(sizeof(uintptr_t));
        memcpy(addressBytes.data(), &address, sizeof(uintptr_t));

        std::string mask(sizeof(uintptr_t), 'x');
        
        uintptr_t startAddress = m_processManager->GetBaseAddress();
        size_t scanSize = m_processManager->GetProcessInfo().moduleSize;

        const size_t chunkSize = 0x1000;
        std::vector<uint8_t> buffer(chunkSize);

        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = std::min(chunkSize, scanSize - offset);
            uintptr_t currentAddress = startAddress + offset;

            if (m_processManager->ReadMemory(currentAddress, buffer.data(), currentChunkSize)) {
                for (size_t i = 0; i <= currentChunkSize - addressBytes.size(); ++i) {
                    if (ComparePattern(buffer.data() + i, addressBytes, mask)) {
                        references.push_back(currentAddress + i);
                    }
                }
            }
        }

        return references;
    }

    void MemoryScanner::InitializeFiveMPatterns() {
        LoadFiveMSignatures();
        UpdateCachedAddresses();
    }

    uintptr_t MemoryScanner::GetPlayerManager() {
        if (m_playerManager == 0) {
            // Pattern for player manager - this would need to be updated for current FiveM version
            Signature playerMgrSig = {
                "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 52",
                "xxx???x?????xxxxxxxx",
                "",
                3,
                "PlayerManager"
            };

            uintptr_t result = FindSignature(playerMgrSig);
            if (result != 0) {
                // Read the relative offset and calculate absolute address
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_playerManager = result + 4 + offset;
            }
        }
        return m_playerManager;
    }

    uintptr_t MemoryScanner::GetLocalPlayer() {
        if (m_localPlayer == 0) {
            uintptr_t playerMgr = GetPlayerManager();
            if (playerMgr != 0) {
                // Read local player from player manager
                m_localPlayer = m_processManager->ReadMemory<uintptr_t>(playerMgr + 0x08);
            }
        }
        return m_localPlayer;
    }

    uintptr_t MemoryScanner::GetEntityList() {
        if (m_entityList == 0) {
            Signature entityListSig = {
                "4C 8B 0D ? ? ? ? 44 8B C1 49 8B 41 08",
                "xxx???xxxxxxxx",
                "",
                3,
                "EntityList"
            };

            uintptr_t result = FindSignature(entityListSig);
            if (result != 0) {
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_entityList = result + 4 + offset;
            }
        }
        return m_entityList;
    }

    uintptr_t MemoryScanner::GetVehicleManager() {
        if (m_vehicleManager == 0) {
            Signature vehicleMgrSig = {
                "48 8B 05 ? ? ? ? 48 8B 50 08 48 85 D2 74 5A",
                "xxx???xxxxxxxxxx",
                "",
                3,
                "VehicleManager"
            };

            uintptr_t result = FindSignature(vehicleMgrSig);
            if (result != 0) {
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_vehicleManager = result + 4 + offset;
            }
        }
        return m_vehicleManager;
    }

    uintptr_t MemoryScanner::GetWorldPtr() {
        if (m_worldPtr == 0) {
            Signature worldSig = {
                "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9",
                "xxx???x?????xxxxxx",
                "",
                3,
                "World"
            };

            uintptr_t result = FindSignature(worldSig);
            if (result != 0) {
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_worldPtr = result + 4 + offset;
            }
        }
        return m_worldPtr;
    }

    uintptr_t MemoryScanner::GetViewMatrix() {
        if (m_viewMatrix == 0) {
            Signature viewMatrixSig = {
                "0F 29 05 ? ? ? ? 0F 28 35 ? ? ? ? 0F 29 0D",
                "xxx???xxxx???xxx",
                "",
                3,
                "ViewMatrix"
            };

            uintptr_t result = FindSignature(viewMatrixSig);
            if (result != 0) {
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_viewMatrix = result + 4 + offset;
            }
        }
        return m_viewMatrix;
    }

    uintptr_t MemoryScanner::GetCameraManager() {
        if (m_cameraManager == 0) {
            Signature cameraSig = {
                "48 8B 0D ? ? ? ? 48 85 C9 74 06 48 8B 01 FF 50 08",
                "xxx???xxxxxxxxxxxx",
                "",
                3,
                "CameraManager"
            };

            uintptr_t result = FindSignature(cameraSig);
            if (result != 0) {
                int32_t offset = m_processManager->ReadMemory<int32_t>(result);
                m_cameraManager = result + 4 + offset;
            }
        }
        return m_cameraManager;
    }

    bool MemoryScanner::IsValidAddress(uintptr_t address) {
        if (address == 0) {
            return false;
        }

        // Check if address is within process memory range
        uintptr_t baseAddress = m_processManager->GetBaseAddress();
        size_t moduleSize = m_processManager->GetProcessInfo().moduleSize;

        return (address >= baseAddress && address < (baseAddress + moduleSize));
    }

    std::vector<uint8_t> MemoryScanner::StringToBytes(const std::string& pattern) {
        std::vector<uint8_t> bytes;
        std::istringstream iss(pattern);
        std::string byteStr;

        while (iss >> byteStr) {
            if (byteStr == "?") {
                bytes.push_back(0x00); // Wildcard byte
            } else {
                bytes.push_back(static_cast<uint8_t>(std::stoul(byteStr, nullptr, 16)));
            }
        }

        return bytes;
    }

    std::string MemoryScanner::BytesToString(const std::vector<uint8_t>& bytes) {
        std::ostringstream oss;
        for (size_t i = 0; i < bytes.size(); ++i) {
            if (i > 0) oss << " ";
            oss << std::hex << std::uppercase << static_cast<int>(bytes[i]);
        }
        return oss.str();
    }

    bool MemoryScanner::ComparePattern(const uint8_t* data, const std::vector<uint8_t>& pattern, const std::string& mask) {
        for (size_t i = 0; i < pattern.size(); ++i) {
            if (mask[i] == 'x' && data[i] != pattern[i]) {
                return false;
            }
        }
        return true;
    }

    bool MemoryScanner::CompareBytes(const uint8_t* data, const uint8_t* pattern, const std::string& mask) {
        for (size_t i = 0; i < mask.length(); ++i) {
            if (mask[i] == 'x' && data[i] != pattern[i]) {
                return false;
            }
        }
        return true;
    }

    std::vector<MEMORY_BASIC_INFORMATION> MemoryScanner::GetMemoryRegions() {
        std::vector<MEMORY_BASIC_INFORMATION> regions;

        if (!m_processManager->IsAttached()) {
            return regions;
        }

        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t address = 0;

        while (VirtualQueryEx(m_processManager->GetProcessHandle(),
                             reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {

            if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_GUARD) == 0) {
                regions.push_back(mbi);
            }

            address = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;
        }

        return regions;
    }

    bool MemoryScanner::IsExecutableRegion(const MEMORY_BASIC_INFORMATION& mbi) {
        return (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) != 0;
    }

    bool MemoryScanner::IsReadableRegion(const MEMORY_BASIC_INFORMATION& mbi) {
        return (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) != 0;
    }

    void MemoryScanner::LoadFiveMSignatures() {
        // Load common FiveM signatures
        m_signatures.clear();

        // These patterns would need to be updated for the current FiveM version
        m_signatures.push_back({
            "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 52",
            "xxx???x?????xxxxxxxx",
            "",
            3,
            "PlayerManager"
        });

        m_signatures.push_back({
            "4C 8B 0D ? ? ? ? 44 8B C1 49 8B 41 08",
            "xxx???xxxxxxxx",
            "",
            3,
            "EntityList"
        });

        m_signatures.push_back({
            "0F 29 05 ? ? ? ? 0F 28 35 ? ? ? ? 0F 29 0D",
            "xxx???xxxx???xxx",
            "",
            3,
            "ViewMatrix"
        });
    }

    void MemoryScanner::UpdateCachedAddresses() {
        // Update all cached addresses
        m_playerManager = 0;
        m_localPlayer = 0;
        m_entityList = 0;
        m_vehicleManager = 0;
        m_worldPtr = 0;
        m_viewMatrix = 0;
        m_cameraManager = 0;

        // Force re-scan on next access
        GetPlayerManager();
        GetEntityList();
        GetWorldPtr();
        GetViewMatrix();
        GetCameraManager();
    }

} // namespace FiveMCheat
