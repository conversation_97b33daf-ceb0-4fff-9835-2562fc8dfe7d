#pragma once

#include <windows.h>
#include <vector>
#include <string>
#include <memory>

namespace FiveMCheat {

    class ProcessManager;

    struct Pattern {
        std::vector<uint8_t> bytes;
        std::string mask;
        std::string name;
        uintptr_t offset;
    };

    struct Signature {
        std::string pattern;
        std::string mask;
        std::string module;
        int offset;
        std::string name;
    };

    class MemoryScanner {
    public:
        MemoryScanner(std::shared_ptr<ProcessManager> processManager);
        ~MemoryScanner();

        // Pattern scanning
        uintptr_t FindPattern(const std::string& pattern, const std::string& mask, 
                             uintptr_t startAddress = 0, size_t scanSize = 0);
        uintptr_t FindPattern(const std::vector<uint8_t>& pattern, const std::string& mask,
                             uintptr_t startAddress = 0, size_t scanSize = 0);
        
        // Signature scanning
        uintptr_t FindSignature(const Signature& signature);
        std::vector<uintptr_t> FindAllPatterns(const std::string& pattern, const std::string& mask);

        // Module scanning
        uintptr_t ScanModule(const std::string& moduleName, const std::string& pattern, const std::string& mask);
        uintptr_t ScanRegion(uintptr_t startAddress, size_t size, const std::string& pattern, const std::string& mask);

        // Advanced scanning
        uintptr_t FindString(const std::string& str, bool caseSensitive = true);
        uintptr_t FindStringW(const std::wstring& str, bool caseSensitive = true);
        uintptr_t FindReference(uintptr_t address);
        std::vector<uintptr_t> FindReferences(uintptr_t address);

        // FiveM specific patterns
        void InitializeFiveMPatterns();
        uintptr_t GetPlayerManager();
        uintptr_t GetLocalPlayer();
        uintptr_t GetEntityList();
        uintptr_t GetVehicleManager();
        uintptr_t GetWorldPtr();
        uintptr_t GetViewMatrix();
        uintptr_t GetCameraManager();

        // Utility functions
        bool IsValidAddress(uintptr_t address);
        std::vector<uint8_t> StringToBytes(const std::string& pattern);
        std::string BytesToString(const std::vector<uint8_t>& bytes);

    private:
        std::shared_ptr<ProcessManager> m_processManager;
        std::vector<Pattern> m_patterns;
        std::vector<Signature> m_signatures;

        // Cached addresses
        uintptr_t m_playerManager;
        uintptr_t m_localPlayer;
        uintptr_t m_entityList;
        uintptr_t m_vehicleManager;
        uintptr_t m_worldPtr;
        uintptr_t m_viewMatrix;
        uintptr_t m_cameraManager;

        // Internal scanning methods
        bool ComparePattern(const uint8_t* data, const std::vector<uint8_t>& pattern, const std::string& mask);
        bool CompareBytes(const uint8_t* data, const uint8_t* pattern, const std::string& mask);
        std::vector<MEMORY_BASIC_INFORMATION> GetMemoryRegions();
        bool IsExecutableRegion(const MEMORY_BASIC_INFORMATION& mbi);
        bool IsReadableRegion(const MEMORY_BASIC_INFORMATION& mbi);

        // FiveM specific offsets and patterns
        void LoadFiveMSignatures();
        void UpdateCachedAddresses();
    };

} // namespace FiveMCheat
