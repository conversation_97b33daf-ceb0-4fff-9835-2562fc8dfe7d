-- Cliente do menu externo para FiveM
local isGodMode = false
local isInvisible = false
local isNoClip = false
local noClipSpeed = 1.0

-- Função para processar comandos do menu externo
function ProcessExternalCommand(command, data, params)
    local ped = PlayerPedId()
    
    if command == "teleport" and params and #params >= 3 then
        local x, y, z = params[1], params[2], params[3]
        SetEntityCoords(ped, x, y, z, false, false, false, true)
        
    elseif command == "spawn_vehicle" and data then
        local model = GetHashKey(data)
        RequestModel(model)
        
        while not HasModelLoaded(model) do
            Wait(1)
        end
        
        local coords = GetEntityCoords(ped)
        local vehicle = CreateVehicle(model, coords.x + 2, coords.y, coords.z, GetEntityHeading(ped), true, false)
        SetPedIntoVehicle(ped, vehicle, -1)
        SetModelAsNoLongerNeeded(model)
        
    elseif command == "set_health" and params and #params >= 1 then
        local health = math.floor(params[1])
        SetEntityHealth(ped, health)
        
    elseif command == "set_armor" and params and #params >= 1 then
        local armor = math.floor(params[1])
        SetPedArmour(ped, armor)
        
    elseif command == "give_money" and params and #params >= 1 then
        local amount = math.floor(params[1])
        -- Trigger server event para adicionar dinheiro
        TriggerServerEvent('external_menu:giveMoney', amount)
        
    elseif command == "set_weather" and data then
        SetWeatherTypeNow(data)
        
    elseif command == "set_time" and params and #params >= 2 then
        local hour = math.floor(params[1])
        local minute = math.floor(params[2])
        NetworkOverrideClockTime(hour, minute, 0)
        
    elseif command == "set_godmode" and params and #params >= 1 then
        isGodMode = params[1] > 0
        SetEntityInvincible(ped, isGodMode)
        
    elseif command == "set_invisible" and params and #params >= 1 then
        isInvisible = params[1] > 0
        SetEntityVisible(ped, not isInvisible, 0)
        
    elseif command == "set_noclip" and params and #params >= 1 then
        isNoClip = params[1] > 0
        
    elseif command == "repair_vehicle" then
        local vehicle = GetVehiclePedIsIn(ped, false)
        if vehicle ~= 0 then
            SetVehicleFixed(vehicle)
            SetVehicleDeformationFixed(vehicle)
            SetVehicleUndriveable(vehicle, false)
            SetVehicleEngineOn(vehicle, true, true)
        end
        
    elseif command == "delete_vehicle" then
        local vehicle = GetVehiclePedIsIn(ped, false)
        if vehicle ~= 0 then
            DeleteVehicle(vehicle)
        end
    end
end

-- Thread principal para NoClip
Citizen.CreateThread(function()
    while true do
        if isNoClip then
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local heading = GetEntityHeading(ped)
            
            -- Desabilitar física
            SetEntityCollision(ped, false, false)
            FreezeEntityPosition(ped, true)
            SetEntityInvincible(ped, true)
            
            -- Controles de movimento
            local speed = noClipSpeed
            if IsControlPressed(0, 21) then -- Shift
                speed = speed * 3
            end
            
            if IsControlPressed(0, 32) then -- W
                local forward = GetEntityForwardVector(ped)
                coords = coords + forward * speed
            end
            if IsControlPressed(0, 33) then -- S
                local forward = GetEntityForwardVector(ped)
                coords = coords - forward * speed
            end
            if IsControlPressed(0, 34) then -- A
                local right = GetEntityRightVector(ped)
                coords = coords - right * speed
            end
            if IsControlPressed(0, 35) then -- D
                local right = GetEntityRightVector(ped)
                coords = coords + right * speed
            end
            if IsControlPressed(0, 44) then -- Q
                coords = vector3(coords.x, coords.y, coords.z - speed)
            end
            if IsControlPressed(0, 38) then -- E
                coords = vector3(coords.x, coords.y, coords.z + speed)
            end
            
            SetEntityCoordsNoOffset(ped, coords.x, coords.y, coords.z, true, true, true)
        else
            local ped = PlayerPedId()
            SetEntityCollision(ped, true, true)
            FreezeEntityPosition(ped, false)
            SetEntityInvincible(ped, isGodMode)
        end
        
        Wait(0)
    end
end)

-- Simulação de recebimento de comandos via named pipe
-- Em uma implementação real, isso seria feito via C++ ou outro método
Citizen.CreateThread(function()
    while true do
        -- Aqui você implementaria a leitura do named pipe
        -- Por enquanto, vamos simular com eventos de rede
        Wait(100)
    end
end)

-- Event handlers para comandos recebidos
RegisterNetEvent('external_menu:executeCommand')
AddEventHandler('external_menu:executeCommand', function(command, data, params)
    ProcessExternalCommand(command, data, params)
end)

-- Comandos de teste (remover em produção)
RegisterCommand('test_external', function(source, args)
    if args[1] == "godmode" then
        ProcessExternalCommand("set_godmode", "", {1})
        print("God mode ativado via comando de teste")
    elseif args[1] == "teleport" then
        ProcessExternalCommand("teleport", "", {-1037.5, -2737.6, 20.2})
        print("Teleportado para aeroporto via comando de teste")
    elseif args[1] == "vehicle" then
        ProcessExternalCommand("spawn_vehicle", "adder", {})
        print("Veículo spawnado via comando de teste")
    end
end)

-- Notificações
function ShowNotification(text)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(text)
    DrawNotification(false, false)
end

-- Mostrar status quando conectar
AddEventHandler('playerSpawned', function()
    ShowNotification("~g~Menu Externo: ~w~Pronto para receber comandos")
end)
