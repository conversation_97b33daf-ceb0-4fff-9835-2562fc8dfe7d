@echo off
echo ================================
echo FiveM Offset Validator Build
echo ================================

REM Check for Visual Studio
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] Visual Studio não encontrado. Configurando ambiente...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64
    if %ERRORLEVEL% NEQ 0 (
        echo [-] Falha ao configurar Visual Studio
        pause
        exit /b 1
    )
    echo [+] Ambiente configurado!
) else (
    echo [+] Visual Studio encontrado!
)

REM Create build directory
if not exist "build_validator" (
    mkdir build_validator
    echo [+] Diretório build_validator criado
) else (
    echo [+] Usando diretório build_validator existente
)

cd build_validator

REM Compile the validator
echo [~] Compilando validador...
cl /EHsc /O2 /std:c++17 ..\src\offset_validator.cpp /Fe:OffsetValidator.exe kernel32.lib user32.lib psapi.lib

if %ERRORLEVEL% EQU 0 (
    echo [+] Compilação concluída com sucesso!
    echo [+] Executável criado: build_validator\OffsetValidator.exe
    echo.
    echo ================================
    echo VALIDADOR COMPILADO COM SUCESSO!
    echo ================================
    echo.
    echo Para usar o validador:
    echo 1. Certifique-se de que o FiveM está rodando
    echo 2. Execute: build_validator\OffsetValidator.exe
    echo.
) else (
    echo [-] Falha na compilação!
)

cd ..
pause
