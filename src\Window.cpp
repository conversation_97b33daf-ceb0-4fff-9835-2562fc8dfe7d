#include "Window.hpp"
#include <imgui.h>

namespace FiveMMenu {

    Window::Window(const std::string& name, const std::string& title)
        : m_name(name)
        , m_title(title)
        , m_isVisible(false)
        , m_size(ImVec2(400, 300))
        , m_position(ImVec2(100, 100))
        , m_flags(ImGuiWindowFlags_None)
        , m_menuManager(nullptr) {
    }

    void Window::HelpMarker(const char* desc) {
        ImGui::TextDisabled("(?)");
        if (ImGui::IsItemHovered()) {
            ImGui::BeginTooltip();
            ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
            ImGui::TextUnformatted(desc);
            ImGui::PopTextWrapPos();
            ImGui::EndTooltip();
        }
    }

    bool Window::ColoredButton(const char* label, const ImVec4& color, const ImVec2& size) {
        ImGui::PushStyleColor(ImGuiCol_Button, color);
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(color.x + 0.1f, color.y + 0.1f, color.z + 0.1f, color.w));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(color.x + 0.2f, color.y + 0.2f, color.z + 0.2f, color.w));
        
        bool result = ImGui::Button(label, size);
        
        ImGui::PopStyleColor(3);
        return result;
    }

    void Window::Spacing(int count) {
        for (int i = 0; i < count; ++i) {
            ImGui::Spacing();
        }
    }

} // namespace FiveMMenu
