#pragma once
#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <string>
#include <vector>
#include <memory>

// ImGui includes
#include "../vendor/imgui/imgui.h"
#include "../vendor/imgui/imgui_impl_win32.h"
#include "../vendor/imgui/imgui_impl_dx11.h"

// Forward declarations
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

class ImGuiMenu {
private:
    // Window and DirectX
    HWND hwnd;
    WNDCLASSEXW wc;
    ID3D11Device* pd3dDevice;
    ID3D11DeviceContext* pd3dDeviceContext;
    IDXGISwapChain* pSwapChain;
    ID3D11RenderTargetView* pMainRenderTargetView;
    
    // Menu state
    bool showMenu;
    bool initialized;
    int currentTab;
    
    // Menu dimensions and style
    ImVec2 menuSize;
    ImVec4 primaryColor;
    ImVec4 secondaryColor;
    ImVec4 accentColor;
    ImVec4 backgroundColor;
    ImVec4 textColor;
    
    // Feature states
    struct AimbotSettings {
        bool enabled = false;
        bool visibleOnly = true;
        bool smoothAim = true;
        float smoothness = 5.0f;
        float fov = 90.0f;
        int targetBone = 0; // 0=Head, 1=Chest, 2=Body
        int aimKey = VK_RBUTTON;
    } aimbot;
    
    struct ESPSettings {
        bool enabled = false;
        bool playerESP = true;
        bool vehicleESP = true;
        bool weaponESP = false;
        bool healthBar = true;
        bool armorBar = true;
        bool distance = true;
        bool skeleton = false;
        float maxDistance = 500.0f;
        ImVec4 playerColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        ImVec4 vehicleColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    } esp;
    
    struct MiscSettings {
        bool godMode = false;
        bool infiniteAmmo = false;
        bool noRecoil = false;
        bool noSpread = false;
        bool fastRun = false;
        bool superJump = false;
        bool noclip = false;
        float speedMultiplier = 1.0f;
        float jumpHeight = 1.0f;
    } misc;
    
    struct VehicleSettings {
        bool godMode = false;
        bool infiniteFuel = false;
        bool noFallDamage = false;
        bool instantRepair = false;
        float speedBoost = 1.0f;
        float torqueMultiplier = 1.0f;
    } vehicle;

public:
    ImGuiMenu();
    ~ImGuiMenu();
    
    // Core functions
    bool Initialize();
    void Shutdown();
    void Render();
    void HandleInput();
    bool IsVisible() const { return showMenu; }
    void Toggle() { showMenu = !showMenu; }
    void Show() { showMenu = true; }
    void Hide() { showMenu = false; }
    
    // Getters for cheat features
    const AimbotSettings& GetAimbotSettings() const { return aimbot; }
    const ESPSettings& GetESPSettings() const { return esp; }
    const MiscSettings& GetMiscSettings() const { return misc; }
    const VehicleSettings& GetVehicleSettings() const { return vehicle; }

private:
    // DirectX setup
    bool CreateDeviceD3D(HWND hWnd);
    void CleanupDeviceD3D();
    void CreateRenderTarget();
    void CleanupRenderTarget();
    
    // Window procedure
    static LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    // Menu rendering functions
    void SetupStyle();
    void RenderMainMenu();
    void RenderAimbotTab();
    void RenderESPTab();
    void RenderMiscTab();
    void RenderVehicleTab();
    void RenderConfigTab();
    
    // Helper functions
    void DrawToggleButton(const char* label, bool* value, const ImVec2& size = ImVec2(0, 0));
    void DrawSliderFloat(const char* label, float* value, float min, float max, const char* format = "%.1f");
    void DrawSliderInt(const char* label, int* value, int min, int max);
    void DrawColorEdit(const char* label, ImVec4* color);
    void DrawCombo(const char* label, int* current_item, const char* const items[], int items_count);
    void DrawHotkey(const char* label, int* key);
    
    // Style helpers
    void PushStyleColors();
    void PopStyleColors();
    void DrawHeader(const char* text);
    void DrawSeparator();
};

// Global menu instance
extern std::unique_ptr<ImGuiMenu> g_Menu;
