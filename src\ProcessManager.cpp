#include "ProcessManager.hpp"
#include <iostream>
#include <psapi.h>
#include <winternl.h>

// Anti-detection includes
#pragma comment(lib, "ntdll.lib")

namespace FiveMCheat {

    ProcessManager::ProcessManager() 
        : m_antiDetectionEnabled(false)
        , m_exceptionHandler(nullptr) {
        
        // Initialize process info
        ZeroMemory(&m_processInfo, sizeof(ProcessInfo));
        
        // Enable debug privileges
        SetDebugPrivileges();
    }

    ProcessManager::~ProcessManager() {
        DetachFromProcess();
        RemoveExceptionHandler();
    }

    bool ProcessManager::AttachToProcess(const std::string& processName) {
        auto processes = FindProcessesByName(processName);
        
        if (processes.empty()) {
            std::cout << "[!] Process not found: " << processName << std::endl;
            return false;
        }

        // Try to attach to the first process found
        for (DWORD pid : processes) {
            if (AttachToProcess(pid)) {
                std::cout << "[+] Successfully attached to " << processName << " (PID: " << pid << ")" << std::endl;
                return true;
            }
        }

        return false;
    }

    bool ProcessManager::AttachToProcess(DWORD processId) {
        // Detach from current process if attached
        DetachFromProcess();

        // Open process with required privileges
        if (!OpenProcessWithPrivileges(processId)) {
            return false;
        }

        // Get base address and module size
        HMODULE hMods[1024];
        DWORD cbNeeded;
        
        if (EnumProcessModules(m_processInfo.processHandle, hMods, sizeof(hMods), &cbNeeded)) {
            MODULEINFO modInfo;
            if (GetModuleInformation(m_processInfo.processHandle, hMods[0], &modInfo, sizeof(modInfo))) {
                m_processInfo.baseAddress = reinterpret_cast<uintptr_t>(modInfo.lpBaseOfDll);
                m_processInfo.moduleSize = modInfo.SizeOfImage;
            }
        }

        // Get process name
        char processName[MAX_PATH];
        if (GetModuleBaseNameA(m_processInfo.processHandle, nullptr, processName, sizeof(processName))) {
            m_processInfo.processName = processName;
        }

        m_processInfo.processId = processId;

        // Enable anti-detection if needed
        if (m_antiDetectionEnabled) {
            EnableAntiDetection();
        }

        return true;
    }

    void ProcessManager::DetachFromProcess() {
        if (m_processInfo.processHandle) {
            CloseProcessHandle();
        }
        ZeroMemory(&m_processInfo, sizeof(ProcessInfo));
    }

    bool ProcessManager::ReadMemory(uintptr_t address, void* buffer, size_t size) {
        if (!IsAttached()) {
            return false;
        }

        SIZE_T bytesRead;
        BOOL result = ReadProcessMemory(m_processInfo.processHandle, 
                                       reinterpret_cast<LPCVOID>(address), 
                                       buffer, size, &bytesRead);
        
        return result && (bytesRead == size);
    }

    bool ProcessManager::WriteMemory(uintptr_t address, const void* buffer, size_t size) {
        if (!IsAttached()) {
            return false;
        }

        // Change memory protection if needed
        DWORD oldProtect;
        VirtualProtectEx(m_processInfo.processHandle, 
                        reinterpret_cast<LPVOID>(address), 
                        size, PAGE_EXECUTE_READWRITE, &oldProtect);

        SIZE_T bytesWritten;
        BOOL result = WriteProcessMemory(m_processInfo.processHandle, 
                                        reinterpret_cast<LPVOID>(address), 
                                        buffer, size, &bytesWritten);

        // Restore original protection
        VirtualProtectEx(m_processInfo.processHandle, 
                        reinterpret_cast<LPVOID>(address), 
                        size, oldProtect, &oldProtect);

        return result && (bytesWritten == size);
    }

    uintptr_t ProcessManager::GetModuleBaseAddress(const std::string& moduleName) {
        if (!IsAttached()) {
            return 0;
        }

        HMODULE hMods[1024];
        DWORD cbNeeded;
        
        if (EnumProcessModules(m_processInfo.processHandle, hMods, sizeof(hMods), &cbNeeded)) {
            for (unsigned int i = 0; i < (cbNeeded / sizeof(HMODULE)); i++) {
                char szModName[MAX_PATH];
                if (GetModuleBaseNameA(m_processInfo.processHandle, hMods[i], szModName, sizeof(szModName))) {
                    if (_stricmp(szModName, moduleName.c_str()) == 0) {
                        return reinterpret_cast<uintptr_t>(hMods[i]);
                    }
                }
            }
        }
        
        return 0;
    }

    size_t ProcessManager::GetModuleSize(const std::string& moduleName) {
        if (!IsAttached()) {
            return 0;
        }

        HMODULE hMod = reinterpret_cast<HMODULE>(GetModuleBaseAddress(moduleName));
        if (!hMod) {
            return 0;
        }

        MODULEINFO modInfo;
        if (GetModuleInformation(m_processInfo.processHandle, hMod, &modInfo, sizeof(modInfo))) {
            return modInfo.SizeOfImage;
        }

        return 0;
    }

    void ProcessManager::EnableAntiDetection() {
        m_antiDetectionEnabled = true;
        
        if (IsAttached()) {
            HideFromDebugger();
            PatchNtQueryInformationProcess();
            InstallExceptionHandler();
        }
    }

    void ProcessManager::DisableAntiDetection() {
        m_antiDetectionEnabled = false;
        RemoveExceptionHandler();
    }

    bool ProcessManager::IsProcessProtected() {
        if (!IsAttached()) {
            return false;
        }

        // Check if process has anti-debugging measures
        BOOL isDebuggerPresent = FALSE;
        CheckRemoteDebuggerPresent(m_processInfo.processHandle, &isDebuggerPresent);
        
        return isDebuggerPresent;
    }

    std::vector<DWORD> ProcessManager::FindProcessesByName(const std::string& processName) {
        std::vector<DWORD> processes;
        
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return processes;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                if (_stricmp(pe32.szExeFile, processName.c_str()) == 0) {
                    processes.push_back(pe32.th32ProcessID);
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return processes;
    }

    bool ProcessManager::IsProcessRunning(const std::string& processName) {
        return !FindProcessesByName(processName).empty();
    }

    bool ProcessManager::ElevatePrivileges() {
        return SetDebugPrivileges();
    }

    bool ProcessManager::OpenProcessWithPrivileges(DWORD processId) {
        // Try different access rights in order of preference
        DWORD accessRights[] = {
            PROCESS_ALL_ACCESS,
            PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION | PROCESS_QUERY_INFORMATION,
            PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION,
            PROCESS_VM_READ | PROCESS_QUERY_INFORMATION
        };

        for (DWORD access : accessRights) {
            HANDLE hProcess = OpenProcess(access, FALSE, processId);
            if (hProcess) {
                m_processInfo.processHandle = hProcess;
                return true;
            }
        }

        DWORD error = GetLastError();
        std::cout << "[!] Failed to open process. Error: " << error << std::endl;
        return false;
    }

    void ProcessManager::CloseProcessHandle() {
        if (m_processInfo.processHandle) {
            CloseHandle(m_processInfo.processHandle);
            m_processInfo.processHandle = nullptr;
        }
    }

    bool ProcessManager::SetDebugPrivileges() {
        HANDLE hToken;
        TOKEN_PRIVILEGES tokenPriv;
        LUID luid;

        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
            return false;
        }

        if (!LookupPrivilegeValue(nullptr, SE_DEBUG_NAME, &luid)) {
            CloseHandle(hToken);
            return false;
        }

        tokenPriv.PrivilegeCount = 1;
        tokenPriv.Privileges[0].Luid = luid;
        tokenPriv.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

        BOOL result = AdjustTokenPrivileges(hToken, FALSE, &tokenPriv, sizeof(TOKEN_PRIVILEGES), nullptr, nullptr);
        CloseHandle(hToken);

        return result && (GetLastError() == ERROR_SUCCESS);
    }

    void ProcessManager::HideFromDebugger() {
        // Hide current process from debugger detection
        typedef NTSTATUS(WINAPI* pNtSetInformationThread)(HANDLE, UINT, PVOID, ULONG);
        
        HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
        if (hNtdll) {
            pNtSetInformationThread NtSetInformationThread = 
                reinterpret_cast<pNtSetInformationThread>(GetProcAddress(hNtdll, "NtSetInformationThread"));
            
            if (NtSetInformationThread) {
                NtSetInformationThread(GetCurrentThread(), 0x11, nullptr, 0);
            }
        }
    }

    void ProcessManager::PatchNtQueryInformationProcess() {
        // Patch NtQueryInformationProcess to hide debugging
        // This is a simplified version - in practice, you'd want more sophisticated patching
        HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
        if (hNtdll) {
            FARPROC pNtQueryInformationProcess = GetProcAddress(hNtdll, "NtQueryInformationProcess");
            if (pNtQueryInformationProcess) {
                // Patch implementation would go here
                // For security reasons, not implementing the actual patch
            }
        }
    }

    LONG WINAPI ProcessManager::VectoredExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo) {
        // Handle exceptions to avoid detection
        if (pExceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_SINGLE_STEP ||
            pExceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_BREAKPOINT) {
            // Skip the exception
            pExceptionInfo->ContextRecord->EFlags |= 0x100; // Set trap flag
            return EXCEPTION_CONTINUE_EXECUTION;
        }
        
        return EXCEPTION_CONTINUE_SEARCH;
    }

    void ProcessManager::InstallExceptionHandler() {
        if (!m_exceptionHandler) {
            m_exceptionHandler = AddVectoredExceptionHandler(1, VectoredExceptionHandler);
        }
    }

    void ProcessManager::RemoveExceptionHandler() {
        if (m_exceptionHandler) {
            RemoveVectoredExceptionHandler(m_exceptionHandler);
            m_exceptionHandler = nullptr;
        }
    }

} // namespace FiveMCheat
