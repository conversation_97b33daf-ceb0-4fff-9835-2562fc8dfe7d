#pragma once

#include "../Window.hpp"
#include <vector>
#include <string>

namespace FiveMMenu {

    struct VehicleInfo {
        std::string name;
        std::string model;
        std::string category;
    };

    class VehicleWindow : public Window {
    public:
        VehicleWindow();
        ~VehicleWindow() override = default;

        void Render() override;
        void Update() override;

    private:
        void RenderSpawnSection();
        void RenderModificationSection();
        void RenderControlSection();
        void RenderFavorites();
        void LoadVehicleDatabase();

        // Categorias de veículos
        std::vector<std::string> m_categories = {
            "Todos", "Super", "Sports", "Sports Classic", "Coupes", 
            "Sedans", "SUVs", "Motorcycles", "Off-Road", "Industrial",
            "Utility", "Vans", "Cycles", "Boats", "Helicopters", "Planes"
        };

        std::vector<VehicleInfo> m_vehicles;
        std::vector<VehicleInfo> m_filteredVehicles;
        std::vector<std::string> m_favoriteVehicles;
        
        int m_selectedCategory = 0;
        int m_selectedVehicle = 0;
        char m_searchBuffer[256] = "";
        char m_customModel[256] = "";

        // Configurações de spawn
        bool m_spawnInVehicle = true;
        bool m_deleteOldVehicle = true;
        bool m_maxUpgrades = false;
        bool m_customPlate = false;
        char m_plateText[9] = "EXTERNAL";

        // Modificações do veículo atual
        bool m_infiniteFuel = false;
        bool m_noCollision = false;
        bool m_autoRepair = false;
        float m_speedMultiplier = 1.0f;
        float m_torqueMultiplier = 1.0f;

        // Cores
        int m_primaryColor[3] = {255, 255, 255};
        int m_secondaryColor[3] = {0, 0, 0};
        bool m_rainbow = false;
    };

} // namespace FiveMMenu
