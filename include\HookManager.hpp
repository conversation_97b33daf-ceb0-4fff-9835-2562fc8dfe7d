#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <memory>
#include <functional>
#include <vector>

// MinHook library for function hooking
#include "MinHook.h"

namespace FiveMCheat {

    class ProcessManager;

    enum class GraphicsAPI {
        Unknown,
        DirectX9,
        DirectX11,
        DirectX12,
        OpenGL,
        Vulkan
    };

    struct HookInfo {
        void* originalFunction;
        void* hookFunction;
        void** trampolineFunction;
        std::string name;
        bool isEnabled;
    };

    class HookManager {
    public:
        HookManager(std::shared_ptr<ProcessManager> processManager);
        ~HookManager();

        // Hook management
        bool Initialize();
        void Shutdown();
        bool IsInitialized() const { return m_initialized; }

        // Graphics API detection
        GraphicsAPI DetectGraphicsAPI();
        bool InitializeGraphicsHooks();

        // DirectX 11 hooks
        bool HookDirectX11();
        void UnhookDirectX11();
        
        // DirectX 9 hooks
        bool HookDirectX9();
        void UnhookDirectX9();

        // OpenGL hooks
        bool HookOpenGL();
        void UnhookOpenGL();

        // Generic hook functions
        bool CreateHook(void* target, void* detour, void** original, const std::string& name);
        bool EnableHook(const std::string& name);
        bool DisableHook(const std::string& name);
        void RemoveHook(const std::string& name);

        // Overlay management
        void SetOverlayCallback(std::function<void()> callback) { m_overlayCallback = callback; }
        bool IsOverlayVisible() const { return m_overlayVisible; }
        void SetOverlayVisible(bool visible) { m_overlayVisible = visible; }

        // Anti-detection features
        void EnableStealthMode();
        void DisableStealthMode();
        bool IsStealthModeEnabled() const { return m_stealthMode; }

        // Utility functions
        HWND GetGameWindow();
        bool IsGameWindowActive();

    private:
        std::shared_ptr<ProcessManager> m_processManager;
        std::vector<HookInfo> m_hooks;
        bool m_initialized;
        bool m_overlayVisible;
        bool m_stealthMode;
        GraphicsAPI m_detectedAPI;
        std::function<void()> m_overlayCallback;

        // DirectX 11 specific
        ID3D11Device* m_d3d11Device;
        ID3D11DeviceContext* m_d3d11Context;
        IDXGISwapChain* m_dxgiSwapChain;
        
        // Hook function pointers
        typedef HRESULT(WINAPI* D3D11Present_t)(IDXGISwapChain*, UINT, UINT);
        typedef HRESULT(WINAPI* D3D11ResizeBuffers_t)(IDXGISwapChain*, UINT, UINT, UINT, DXGI_FORMAT, UINT);
        
        D3D11Present_t m_originalD3D11Present;
        D3D11ResizeBuffers_t m_originalD3D11ResizeBuffers;

        // DirectX 9 specific
        typedef HRESULT(WINAPI* D3D9Present_t)(LPDIRECT3DDEVICE9, const RECT*, const RECT*, HWND, const RGNDATA*);
        typedef HRESULT(WINAPI* D3D9Reset_t)(LPDIRECT3DDEVICE9, D3DPRESENT_PARAMETERS*);
        
        D3D9Present_t m_originalD3D9Present;
        D3D9Reset_t m_originalD3D9Reset;

        // OpenGL specific
        typedef BOOL(WINAPI* wglSwapBuffers_t)(HDC);
        wglSwapBuffers_t m_originalwglSwapBuffers;

        // Internal methods
        bool FindDirectX11Functions();
        bool FindDirectX9Functions();
        bool FindOpenGLFunctions();
        
        void InitializeImGui();
        void ShutdownImGui();
        void RenderOverlay();

        // Hook implementations
        static HRESULT WINAPI HookedD3D11Present(IDXGISwapChain* swapChain, UINT syncInterval, UINT flags);
        static HRESULT WINAPI HookedD3D11ResizeBuffers(IDXGISwapChain* swapChain, UINT bufferCount, UINT width, UINT height, DXGI_FORMAT newFormat, UINT swapChainFlags);
        
        static HRESULT WINAPI HookedD3D9Present(LPDIRECT3DDEVICE9 device, const RECT* sourceRect, const RECT* destRect, HWND destWindowOverride, const RGNDATA* dirtyRegion);
        static HRESULT WINAPI HookedD3D9Reset(LPDIRECT3DDEVICE9 device, D3DPRESENT_PARAMETERS* presentationParameters);
        
        static BOOL WINAPI HookedwglSwapBuffers(HDC hdc);

        // Anti-detection methods
        void HideHooksFromDetection();
        void RestoreOriginalBytes();
        bool IsHookDetected(const std::string& hookName);

        // Stealth techniques
        void PatchHookDetection();
        void UnpatchHookDetection();
        void RandomizeHookAddresses();

        // Static instance for hook callbacks
        static HookManager* s_instance;
    };

} // namespace FiveMCheat
