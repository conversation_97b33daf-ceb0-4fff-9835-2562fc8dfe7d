#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <map>
#include <string>
#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>

class SimpleOffsetDumper {
private:
    HANDLE processHandle;
    DWORD processId;
    uintptr_t baseAddress;
    std::string lastError;
    std::map<std::string, uintptr_t> foundOffsets;
    
    struct Signature {
        std::string name;
        std::string pattern;
        std::string description;
        bool isRelative;
        int operandLocation;
        int operandLength;
        int additionalOffset;
    };
    
    std::vector<Signature> signatures;

public:
    SimpleOffsetDumper() : processHandle(nullptr), processId(0), baseAddress(0) {}
    
    ~SimpleOffsetDumper() {
        if (processHandle) {
            CloseHandle(processHandle);
        }
    }
    
    bool AttachToProcess(const std::string& processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) {
            lastError = "Failed to create process snapshot";
            return false;
        }
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        bool found = false;
        if (Process32First(snapshot, &pe32)) {
            do {
                // Convert WCHAR to char for comparison
                char exeFileName[MAX_PATH];
                WideCharToMultiByte(CP_UTF8, 0, pe32.szExeFile, -1, exeFileName, MAX_PATH, NULL, NULL);
                if (_stricmp(exeFileName, processName.c_str()) == 0) {
                    processId = pe32.th32ProcessID;
                    found = true;
                    break;
                }
            } while (Process32Next(snapshot, &pe32));
        }
        
        CloseHandle(snapshot);
        
        if (!found) {
            lastError = "Process not found: " + processName;
            return false;
        }
        
        processHandle = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (!processHandle) {
            lastError = "Failed to open process";
            return false;
        }
        
        // Get base address
        HMODULE hMods[1024];
        DWORD cbNeeded;
        if (EnumProcessModules(processHandle, hMods, sizeof(hMods), &cbNeeded)) {
            baseAddress = reinterpret_cast<uintptr_t>(hMods[0]);
        }
        
        return true;
    }
    
    DWORD GetProcessId() const { return processId; }
    uintptr_t GetBaseAddress() const { return baseAddress; }
    std::string GetLastError() const { return lastError; }
    
    bool LoadConfig(const std::string& configPath) {
        // Hardcoded signatures for FiveM (simplified version)
        signatures = {
            {"WorldPtr", "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9", "Pointer to the game world structure", true, 3, 4, 0},
            {"PlayerManager", "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 52", "Player manager structure", true, 3, 4, 0},
            {"EntityList", "4C 8B 0D ? ? ? ? 44 8B C1 49 8B 41 08", "Entity list pointer", true, 3, 4, 0},
            {"LocalPlayer", "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 50 ?", "Local player pointer", true, 3, 4, 0},
            {"ViewMatrix", "0F 29 05 ? ? ? ? 0F 28 35 ? ? ? ? 0F 29 0D", "View matrix for world to screen conversion", true, 3, 4, 0}
        };
        
        return true;
    }
    
    size_t GetSignatureCount() const { return signatures.size(); }
    
    bool DumpOffsets() {
        foundOffsets.clear();
        
        for (const auto& sig : signatures) {
            uintptr_t address = FindPattern(sig.pattern);
            if (address != 0) {
                if (sig.isRelative) {
                    // Read relative offset
                    DWORD relativeOffset = 0;
                    if (ReadProcessMemory(processHandle, reinterpret_cast<LPCVOID>(address + sig.operandLocation), 
                                        &relativeOffset, sig.operandLength, nullptr)) {
                        address = address + sig.operandLocation + sig.operandLength + relativeOffset + sig.additionalOffset;
                    }
                }
                foundOffsets[sig.name] = address;
                std::cout << "[+] " << sig.name << ": 0x" << std::hex << address << std::dec << std::endl;
            } else {
                std::cout << "[-] " << sig.name << ": Not found" << std::endl;
            }
        }
        
        return !foundOffsets.empty();
    }
    
    const std::map<std::string, uintptr_t>& GetResults() const {
        return foundOffsets;
    }
    
    bool SaveAsHeader(const std::string& filename) {
        std::ofstream file(filename);
        if (!file.is_open()) {
            lastError = "Failed to create header file";
            return false;
        }
        
        file << "#pragma once\n\n";
        file << "// FiveM Offsets - Generated by FiveM Offset Dumper\n";
        file << "// Base Address: 0x" << std::hex << baseAddress << std::dec << "\n\n";
        file << "namespace FiveMOffsets {\n";
        
        for (const auto& offset : foundOffsets) {
            file << "    constexpr uintptr_t " << offset.first << " = 0x" 
                 << std::hex << offset.second << std::dec << ";\n";
        }
        
        file << "}\n";
        file.close();
        
        return true;
    }
    
    bool SaveAsCheatEngineTable(const std::string& filename) {
        std::ofstream file(filename);
        if (!file.is_open()) {
            lastError = "Failed to create Cheat Engine table";
            return false;
        }
        
        file << "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n";
        file << "<CheatTable>\n";
        file << "  <CheatEntries>\n";
        
        for (const auto& offset : foundOffsets) {
            file << "    <CheatEntry>\n";
            file << "      <ID>" << offset.first << "</ID>\n";
            file << "      <Description>\"" << offset.first << "\"</Description>\n";
            file << "      <LastState Value=\"\" RealAddress=\"" << std::hex << offset.second << std::dec << "\"/>\n";
            file << "      <VariableType>8 Bytes</VariableType>\n";
            file << "      <Address>" << std::hex << offset.second << std::dec << "</Address>\n";
            file << "    </CheatEntry>\n";
        }
        
        file << "  </CheatEntries>\n";
        file << "</CheatTable>\n";
        file.close();
        
        return true;
    }

private:
    uintptr_t FindPattern(const std::string& pattern) {
        std::vector<int> patternBytes = ParsePattern(pattern);
        if (patternBytes.empty()) return 0;
        
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t address = baseAddress;
        
        while (VirtualQueryEx(processHandle, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
            if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_GUARD) == 0 && 
                (mbi.Protect & PAGE_NOACCESS) == 0) {
                
                std::vector<BYTE> buffer(mbi.RegionSize);
                SIZE_T bytesRead;
                
                if (ReadProcessMemory(processHandle, mbi.BaseAddress, buffer.data(), 
                                    mbi.RegionSize, &bytesRead)) {
                    
                    for (size_t i = 0; i <= bytesRead - patternBytes.size(); ++i) {
                        bool found = true;
                        for (size_t j = 0; j < patternBytes.size(); ++j) {
                            if (patternBytes[j] != -1 && patternBytes[j] != buffer[i + j]) {
                                found = false;
                                break;
                            }
                        }
                        if (found) {
                            return reinterpret_cast<uintptr_t>(mbi.BaseAddress) + i;
                        }
                    }
                }
            }
            
            address = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;
        }
        
        return 0;
    }
    
    std::vector<int> ParsePattern(const std::string& pattern) {
        std::vector<int> result;
        std::istringstream iss(pattern);
        std::string token;
        
        while (iss >> token) {
            if (token == "?") {
                result.push_back(-1);
            } else {
                try {
                    result.push_back(std::stoi(token, nullptr, 16));
                } catch (...) {
                    return {};
                }
            }
        }
        
        return result;
    }
};
