# Validação de Offsets - FiveM

## 1. Verificação Manual com Cheat Engine

### Passo a Passo:
1. **<PERSON>bra o Cheat Engine**
2. **Anexe ao processo FiveM** (mesmo PID: 15348)
3. **Vá para o endereço encontrado**: `0x7fffa2db9c28`
4. **Verifique se é um ponteiro válido**:
   - Clique com botão direito → "Browse this memory region"
   - Se mostrar dados válidos (não 00 00 00 00), é um bom sinal

### Validação do LocalPlayer:
- O endereço deve apontar para uma estrutura de jogador
- Deve conter valores que fazem sentido (coordenadas, vida, etc.)
- Os valores devem mudar quando você se move no jogo

## 2. Verificação Programática

### Teste de Leitura de Memória:
```cpp
// Ler o ponteiro
uintptr_t playerPtr = 0;
ReadProcessMemory(hProcess, (LPCVOID)0x7fffa2db9c28, &playerPtr, sizeof(playerPtr), nullptr);

// Verificar se é um endereço válido
if (playerPtr > 0x10000 && playerPtr < 0x7FFFFFFFFFFF) {
    // Tentar ler dados do jogador
    float coords[3];
    ReadProcessMemory(hProcess, (LPCVOID)(playerPtr + 0x90), coords, sizeof(coords), nullptr);
    
    // Coordenadas devem estar em um range válido do GTA V
    if (coords[0] > -4000 && coords[0] < 4000 &&
        coords[1] > -4000 && coords[1] < 4000) {
        printf("Offset válido! Coordenadas: %.2f, %.2f, %.2f\n", 
               coords[0], coords[1], coords[2]);
    }
}
```

## 3. Sinais de Offset Válido

### ✅ Bons Sinais:
- **Endereço estável**: Não muda entre execuções do jogo
- **Dados coerentes**: Valores fazem sentido para um jogador
- **Mudanças dinâmicas**: Valores mudam quando você age no jogo
- **Range válido**: Coordenadas dentro do mapa do GTA V (-4000 a +4000)

### ❌ Sinais de Problema:
- **Endereço inválido**: 0x00000000 ou muito baixo
- **Dados estáticos**: Valores nunca mudam
- **Valores absurdos**: Coordenadas como 999999999
- **Acesso negado**: Não consegue ler a memória

## 4. Testes Específicos para LocalPlayer

### Teste de Coordenadas:
1. **Anote sua posição atual** no jogo
2. **Leia as coordenadas** do offset + 0x90
3. **Mova-se no jogo**
4. **Leia novamente** - devem ter mudado

### Teste de Vida:
1. **Leia a vida** do offset + 0x280
2. **Tome dano no jogo**
3. **Leia novamente** - deve ter diminuído

## 5. Validação Automática

### Script de Teste:
```cpp
bool ValidateLocalPlayerOffset(HANDLE hProcess, uintptr_t offset) {
    uintptr_t playerPtr = 0;
    if (!ReadProcessMemory(hProcess, (LPCVOID)offset, &playerPtr, sizeof(playerPtr), nullptr)) {
        return false;
    }
    
    if (playerPtr < 0x10000) return false;
    
    // Teste 1: Ler coordenadas
    float coords[3];
    if (ReadProcessMemory(hProcess, (LPCVOID)(playerPtr + 0x90), coords, sizeof(coords), nullptr)) {
        if (coords[0] < -4000 || coords[0] > 4000 ||
            coords[1] < -4000 || coords[1] > 4000) {
            return false;
        }
    }
    
    // Teste 2: Ler vida
    float health;
    if (ReadProcessMemory(hProcess, (LPCVOID)(playerPtr + 0x280), &health, sizeof(health), nullptr)) {
        if (health < 0 || health > 1000) {
            return false;
        }
    }
    
    return true;
}
```

## 6. Comparação com Offsets Conhecidos

### Offsets Típicos do GTA V/FiveM:
- **Coordenadas**: Geralmente em +0x90 do player
- **Vida**: Geralmente em +0x280 do player  
- **Armadura**: Geralmente em +0x14E0 do player

### Verificação de Padrão:
Se o offset encontrado + estes offsets conhecidos retornam dados válidos,
é uma forte indicação de que o offset está correto.

## 7. Teste de Estabilidade

### Reinicie o Jogo:
1. **Feche o FiveM**
2. **Execute o offset dumper novamente**
3. **Compare os resultados**
4. **Offsets devem ser similares** (podem variar ligeiramente devido ao ASLR)

## 8. Validação Cruzada

### Use Múltiplas Assinaturas:
- Se várias assinaturas apontam para a mesma região de memória
- É uma boa indicação de que a área está correta
- Offsets relacionados devem fazer sentido entre si

## Conclusão

Para o offset encontrado `0x7fffa2db9c28`:
1. **Teste no Cheat Engine** primeiro
2. **Implemente validação programática**
3. **Verifique se os dados mudam** quando você age no jogo
4. **Compare com offsets conhecidos** da comunidade

Se passar em todos esses testes, o offset é muito provavelmente válido e utilizável.
