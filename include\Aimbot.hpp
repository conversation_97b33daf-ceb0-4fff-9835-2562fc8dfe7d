#pragma once

#include "GameStructures.hpp"
#include <memory>
#include <vector>
#include <chrono>

namespace FiveMCheat {

    class ProcessManager;
    class MemoryScanner;

    enum class AimbotMode {
        Off,
        Legit,      // Smooth, human-like aiming
        Rage,       // Instant snapping
        Silent      // No visual movement, only bullet redirection
    };

    enum class TargetBone {
        Head,
        Neck,
        Chest,
        Stomach,
        Pelvis,
        LeftArm,
        RightArm,
        LeftLeg,
        RightLeg,
        Closest     // Closest visible bone
    };

    enum class TargetPriority {
        Distance,   // Closest target
        Health,     // Lowest health
        Crosshair,  // Closest to crosshair
        Threat      // Most dangerous (based on weapon, aim direction)
    };

    struct AimbotSettings {
        bool enabled = false;
        AimbotMode mode = AimbotMode::Legit;
        TargetBone targetBone = TargetBone::Head;
        TargetPriority priority = TargetPriority::Distance;
        
        // FOV settings
        float fov = 90.0f;
        bool drawFOV = true;
        
        // Smooth settings
        float smoothX = 5.0f;
        float smoothY = 5.0f;
        bool humanization = true;
        
        // Range settings
        float maxDistance = 500.0f;
        float minDistance = 5.0f;
        
        // Visibility checks
        bool visibilityCheck = true;
        bool wallbangCheck = false;
        
        // Target filtering
        bool targetPlayers = true;
        bool targetNPCs = false;
        bool targetVehicles = false;
        bool ignoreTeam = true;
        bool ignoreFriends = true;
        
        // Advanced features
        bool prediction = true;
        float predictionTime = 0.1f;
        bool autoShoot = false;
        bool autoScope = false;
        bool recoilControl = false;
        
        // Anti-aim detection
        bool antiAimDetection = true;
        float maxAngleChange = 180.0f;
        
        // Keybinds
        int aimKey = VK_RBUTTON;  // Right mouse button
        int toggleKey = VK_F6;
        bool holdToAim = true;
    };

    struct Target {
        uintptr_t entityPtr;
        Vector3 position;
        Vector3 velocity;
        Vector3 headPosition;
        Vector3 chestPosition;
        float distance;
        float health;
        float armor;
        bool isVisible;
        bool isPlayer;
        bool isTeammate;
        bool isFriend;
        int playerId;
        std::string name;
        float threatLevel;
        std::chrono::steady_clock::time_point lastSeen;
    };

    class Aimbot {
    public:
        Aimbot(std::shared_ptr<ProcessManager> processManager, 
               std::shared_ptr<MemoryScanner> memoryScanner);
        ~Aimbot();

        // Main functions
        void Update();
        void Render();
        
        // Settings
        AimbotSettings& GetSettings() { return m_settings; }
        void SetSettings(const AimbotSettings& settings) { m_settings = settings; }
        
        // Control
        void Enable() { m_settings.enabled = true; }
        void Disable() { m_settings.enabled = false; }
        void Toggle() { m_settings.enabled = !m_settings.enabled; }
        bool IsEnabled() const { return m_settings.enabled; }
        
        // Target management
        std::vector<Target> GetValidTargets();
        Target* GetBestTarget();
        Target* GetCurrentTarget() { return m_currentTarget; }
        
        // Aiming functions
        void AimAtTarget(const Target& target);
        Vector3 GetTargetBonePosition(const Target& target, TargetBone bone);
        Vector3 PredictTargetPosition(const Target& target);
        
        // Utility functions
        bool IsTargetValid(const Target& target);
        bool IsTargetVisible(const Target& target);
        bool IsInFOV(const Vector3& targetPos);
        float GetDistanceToTarget(const Target& target);
        float GetAngleToTarget(const Vector3& targetPos);
        
        // Math functions
        Vector3 WorldToScreen(const Vector3& worldPos);
        Vector3 ScreenToWorld(const Vector2& screenPos);
        Vector2 GetAnglesTo(const Vector3& targetPos);
        void SetViewAngles(const Vector2& angles);
        Vector2 GetViewAngles();
        
        // Anti-detection
        void AddHumanization();
        void RandomizeAimPath();
        bool ShouldSkipFrame();
        
        // Statistics
        int GetHitCount() const { return m_hitCount; }
        int GetShotCount() const { return m_shotCount; }
        float GetAccuracy() const { return m_shotCount > 0 ? (float)m_hitCount / m_shotCount : 0.0f; }
        void ResetStats() { m_hitCount = 0; m_shotCount = 0; }

    private:
        std::shared_ptr<ProcessManager> m_processManager;
        std::shared_ptr<MemoryScanner> m_memoryScanner;
        AimbotSettings m_settings;
        
        // Current state
        Target* m_currentTarget;
        std::vector<Target> m_targets;
        Vector2 m_currentAngles;
        Vector2 m_targetAngles;
        
        // Timing
        std::chrono::steady_clock::time_point m_lastUpdate;
        std::chrono::steady_clock::time_point m_lastShot;
        std::chrono::steady_clock::time_point m_lastTargetSwitch;
        
        // Statistics
        int m_hitCount;
        int m_shotCount;
        
        // Humanization
        std::vector<Vector2> m_aimPath;
        float m_humanizationFactor;
        std::chrono::steady_clock::time_point m_lastHumanization;
        
        // Anti-detection
        bool m_skipNextFrame;
        float m_randomDelay;
        
        // Game state
        uintptr_t m_localPlayer;
        Vector3 m_localPlayerPos;
        Vector3 m_cameraPos;
        Matrix4x4 m_viewMatrix;
        
        // Internal methods
        void UpdateGameState();
        void UpdateTargets();
        void UpdateCurrentTarget();
        void ProcessAiming();
        
        // Target validation
        bool IsPlayerValid(uintptr_t playerPtr);
        bool IsEntityValid(uintptr_t entityPtr);
        bool CheckLineOfSight(const Vector3& start, const Vector3& end);
        bool IsTeammate(uintptr_t playerPtr);
        bool IsFriend(uintptr_t playerPtr);
        
        // Bone calculations
        Vector3 GetBonePosition(uintptr_t entityPtr, int boneId);
        int GetBoneId(TargetBone bone);
        
        // Smooth aiming
        Vector2 SmoothAngles(const Vector2& currentAngles, const Vector2& targetAngles, float smoothFactor);
        void ApplyRecoilControl();
        
        // Prediction
        Vector3 CalculateInterceptionPoint(const Vector3& targetPos, const Vector3& targetVel, 
                                          const Vector3& shooterPos, float bulletSpeed);
        
        // Math utilities
        float NormalizeAngle(float angle);
        Vector2 NormalizeAngles(const Vector2& angles);
        float GetFOVDistance(const Vector3& targetPos);
        bool IsAngleValid(const Vector2& angles);
        
        // Memory addresses
        uintptr_t m_viewAnglesPtr;
        uintptr_t m_localPlayerPtr;
        uintptr_t m_entityListPtr;
        uintptr_t m_viewMatrixPtr;
        
        // Offsets (would be updated for current game version)
        static constexpr int OFFSET_HEALTH = 0x280;
        static constexpr int OFFSET_ARMOR = 0x14B4;
        static constexpr int OFFSET_TEAM = 0xF4;
        static constexpr int OFFSET_DORMANT = 0xED;
        static constexpr int OFFSET_LIFESTATE = 0x25F;
        static constexpr int OFFSET_ORIGIN = 0x138;
        static constexpr int OFFSET_VELOCITY = 0x114;
        static constexpr int OFFSET_BONE_MATRIX = 0x26A8;
    };

} // namespace FiveMCheat
