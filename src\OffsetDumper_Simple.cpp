#include "OffsetDumper.hpp"
#include "ProcessManager.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <psapi.h>
#include <algorithm>

// JSON library for config parsing
#include <nlohmann/json.hpp>
using json = nlohmann::json;

namespace FiveMCheat {

// Estrutura simplificada para assinaturas
struct SimpleSignature {
    std::string name;
    std::string pattern;
    std::string mask;
    bool isRelative;
    int operandLocation;
    int operandLength;
    std::string description;
};

// Estrutura para resultado do dump
struct SimpleDumpResult {
    std::string name;
    uintptr_t address;
    bool found;
    std::string description;
};

class SimpleOffsetDumper {
private:
    std::shared_ptr<ProcessManager> m_processManager;
    std::vector<SimpleSignature> m_signatures;
    std::vector<SimpleDumpResult> m_results;
    std::string m_lastError;

public:
    SimpleOffsetDumper() {
        m_processManager = std::make_shared<ProcessManager>();
    }

    bool LoadConfig(const std::string& configPath) {
        try {
            std::ifstream file(configPath);
            if (!file.is_open()) {
                m_lastError = "Could not open config file: " + configPath;
                return false;
            }

            json config;
            file >> config;

            m_signatures.clear();
            for (const auto& sig : config["signatures"]) {
                SimpleSignature signature;
                signature.name = sig["name"];
                signature.pattern = sig["pattern"];
                signature.isRelative = sig.value("isRelative", false);
                signature.operandLocation = sig.value("operandLocation", 3);
                signature.operandLength = sig.value("operandLength", 4);
                signature.description = sig.value("description", "");
                
                // Generate mask from pattern
                std::istringstream iss(signature.pattern);
                std::string byte;
                signature.mask = "";
                while (iss >> byte) {
                    signature.mask += (byte == "?") ? "?" : "x";
                }
                
                m_signatures.push_back(signature);
            }

            return true;
        } catch (const std::exception& e) {
            m_lastError = "Error parsing config: " + std::string(e.what());
            return false;
        }
    }

    bool AttachToProcess(const std::string& processName) {
        return m_processManager->AttachToProcess(processName);
    }

    DWORD GetProcessId() const {
        return m_processManager->GetProcessId();
    }

    uintptr_t GetBaseAddress() const {
        return m_processManager->GetBaseAddress();
    }

    bool DumpOffsets() {
        m_results.clear();
        
        for (const auto& signature : m_signatures) {
            std::cout << "[~] Scanning for: " << signature.name << std::endl;
            
            uintptr_t address = ScanForPattern(signature);
            
            SimpleDumpResult result;
            result.name = signature.name;
            result.address = address;
            result.found = (address != 0);
            result.description = signature.description;
            
            if (result.found) {
                std::cout << "[+] Found " << signature.name << " at: 0x" << std::hex << address << std::dec << std::endl;
            } else {
                std::cout << "[-] Failed to find: " << signature.name << std::endl;
            }
            
            m_results.push_back(result);
        }
        
        return true;
    }

    const std::vector<SimpleDumpResult>& GetResults() const {
        return m_results;
    }

    size_t GetSignatureCount() const {
        return m_signatures.size();
    }

    const std::string& GetLastError() const {
        return m_lastError;
    }

    bool SaveAsHeader(const std::string& filePath) {
        try {
            std::filesystem::create_directories(std::filesystem::path(filePath).parent_path());
            
            std::ofstream file(filePath);
            if (!file.is_open()) {
                m_lastError = "Could not create header file: " + filePath;
                return false;
            }
            
            file << "#pragma once\n\n";
            file << "// Auto-generated FiveM offsets\n";
            file << "// Generated by FiveM Offset Dumper\n\n";
            file << "#include <cstdint>\n\n";
            file << "namespace FiveMOffsets {\n\n";
            
            for (const auto& result : m_results) {
                if (result.found) {
                    file << "    constexpr uintptr_t " << result.name << " = 0x" 
                         << std::hex << result.address << std::dec << ";\n";
                } else {
                    file << "    // " << result.name << " - NOT FOUND\n";
                }
            }
            
            file << "\n} // namespace FiveMOffsets\n";
            
            return true;
        } catch (const std::exception& e) {
            m_lastError = "Error saving header: " + std::string(e.what());
            return false;
        }
    }

    bool SaveAsCheatEngineTable(const std::string& filePath) {
        try {
            std::filesystem::create_directories(std::filesystem::path(filePath).parent_path());
            
            std::ofstream file(filePath);
            if (!file.is_open()) {
                m_lastError = "Could not create Cheat Engine table: " + filePath;
                return false;
            }
            
            file << "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n";
            file << "<CheatTable>\n";
            file << "  <CheatEntries>\n";
            
            for (const auto& result : m_results) {
                if (result.found) {
                    file << "    <CheatEntry>\n";
                    file << "      <ID>" << result.name << "</ID>\n";
                    file << "      <Description>\"" << result.name << "\"</Description>\n";
                    file << "      <LastState Value=\"\" RealAddress=\"" 
                         << std::hex << result.address << std::dec << "\"/>\n";
                    file << "      <VariableType>8 Bytes</VariableType>\n";
                    file << "      <Address>" << std::hex << result.address << std::dec << "</Address>\n";
                    file << "    </CheatEntry>\n";
                }
            }
            
            file << "  </CheatEntries>\n";
            file << "</CheatTable>\n";
            
            return true;
        } catch (const std::exception& e) {
            m_lastError = "Error saving Cheat Engine table: " + std::string(e.what());
            return false;
        }
    }

private:
    uintptr_t ScanForPattern(const SimpleSignature& signature) {
        // Convert pattern string to bytes
        std::vector<uint8_t> pattern;
        std::istringstream iss(signature.pattern);
        std::string byte;
        
        while (iss >> byte) {
            if (byte != "?") {
                pattern.push_back(static_cast<uint8_t>(std::stoul(byte, nullptr, 16)));
            } else {
                pattern.push_back(0x00);
            }
        }
        
        if (pattern.empty()) return 0;
        
        // Get module information
        HMODULE hModule = nullptr;
        DWORD cbNeeded;
        if (!EnumProcessModules(m_processManager->GetProcessHandle(), &hModule, sizeof(hModule), &cbNeeded)) {
            return 0;
        }
        
        MODULEINFO moduleInfo;
        if (!GetModuleInformation(m_processManager->GetProcessHandle(), hModule, &moduleInfo, sizeof(moduleInfo))) {
            return 0;
        }
        
        uintptr_t baseAddress = reinterpret_cast<uintptr_t>(moduleInfo.lpBaseOfDll);
        size_t moduleSize = moduleInfo.SizeOfImage;
        
        // Read memory in chunks
        const size_t chunkSize = 0x1000; // 4KB chunks
        std::vector<uint8_t> buffer(chunkSize);
        
        for (size_t offset = 0; offset < moduleSize; offset += chunkSize) {
            size_t readSize = std::min(chunkSize, moduleSize - offset);
            
            if (!m_processManager->ReadMemory(baseAddress + offset, buffer.data(), readSize)) {
                continue;
            }
            
            // Search for pattern in this chunk
            for (size_t i = 0; i <= readSize - pattern.size(); ++i) {
                bool found = true;
                for (size_t j = 0; j < pattern.size(); ++j) {
                    if (signature.mask[j] == 'x' && buffer[i + j] != pattern[j]) {
                        found = false;
                        break;
                    }
                }
                
                if (found) {
                    uintptr_t foundAddress = baseAddress + offset + i;
                    
                    // Handle relative addressing
                    if (signature.isRelative) {
                        uint32_t relativeOffset;
                        if (!m_processManager->ReadMemory(foundAddress + signature.operandLocation, 
                                                        &relativeOffset, 
                                                        signature.operandLength)) {
                            continue;
                        }
                        
                        foundAddress = foundAddress + signature.operandLocation + signature.operandLength + relativeOffset;
                    }
                    
                    return foundAddress;
                }
            }
        }
        
        return 0;
    }
};

} // namespace FiveMCheat
