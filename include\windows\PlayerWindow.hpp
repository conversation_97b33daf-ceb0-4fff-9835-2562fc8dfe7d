#pragma once

#include "../Window.hpp"

namespace FiveMMenu {

    class PlayerWindow : public Window {
    public:
        PlayerWindow();
        ~PlayerWindow() override = default;

        void Render() override;
        void Update() override;

    private:
        void RenderHealthSection();
        void RenderMovementSection();
        void RenderMoneySection();
        void RenderWeaponsSection();
        void RenderMiscSection();

        // Estado interno
        int m_healthValue = 100;
        int m_armorValue = 100;
        int m_moneyAmount = 1000;
        float m_walkSpeed = 1.0f;
        float m_runSpeed = 1.0f;
        
        bool m_godMode = false;
        bool m_invisible = false;
        bool m_noClip = false;
        bool m_infiniteStamina = false;
        bool m_fastRun = false;
        bool m_superJump = false;

        // Armas disponíveis
        std::vector<std::pair<std::string, std::string>> m_weapons = {
            {"Pistol", "weapon_pistol"},
            {"Combat Pistol", "weapon_combatpistol"},
            {"SMG", "weapon_smg"},
            {"Assault Rifle", "weapon_assaultrifle"},
            {"Carbine Rifle", "weapon_carbinerifle"},
            {"Sniper Rifle", "weapon_sniperrifle"},
            {"RPG", "weapon_rpg"},
            {"Grenade", "weapon_grenade"}
        };
        
        int m_selectedWeapon = 0;
        int m_weaponAmmo = 250;
    };

} // namespace FiveMMenu
