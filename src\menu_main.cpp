#include "ImGuiMenu.hpp"
#include <iostream>
#include <memory>

// Global menu instance
std::unique_ptr<ImGuiMenu> g_Menu = nullptr;

int main() {
    std::cout << "=== SKECH FIVEM CHEAT MENU ===" << std::endl;
    std::cout << "Inicializando interface..." << std::endl;
    
    // Create menu instance
    g_Menu = std::make_unique<ImGuiMenu>();
    
    // Initialize the menu
    if (!g_Menu->Initialize()) {
        std::cerr << "Falha ao inicializar o menu!" << std::endl;
        return -1;
    }
    
    std::cout << "Menu inicializado com sucesso!" << std::endl;
    std::cout << "Pressione INSERT para abrir/fechar o menu" << std::endl;
    std::cout << "Feche a janela para sair" << std::endl;
    
    // Show the menu initially
    g_Menu->Show();
    
    // Main loop
    while (g_Menu->IsVisible()) {
        // Handle input
        g_Menu->HandleInput();
        
        // Render the menu
        g_Menu->Render();
        
        // Small delay to prevent high CPU usage
        Sleep(1);
    }
    
    std::cout << "Encerrando menu..." << std::endl;
    
    // Cleanup
    g_Menu->Shutdown();
    g_Menu.reset();
    
    return 0;
}
