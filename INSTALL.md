# Guia de Instalação - FiveM External Menu

## 📋 Pré-requisitos

### Software Necessário
- **Windows 10/11** (64-bit)
- **Visual Studio 2022** ou **Visual Studio Build Tools 2022**
- **CMake 3.16+** ([Download](https://cmake.org/download/))
- **Git** ([Download](https://git-scm.com/download/win))
- **vcpkg** (gerenciador de pacotes C++) - Opcional mas recomendado

### Dependências
- **GLFW3** - Para janelas e input
- **GLEW** - Para OpenGL
- **Dear ImGui** - Para interface gráfica

## 🚀 Instalação Passo a Passo

### 1. Preparar o Ambiente

```bash
# Clonar o repositório
git clone <seu-repositorio>
cd cheat-externo-fivem

# Executar script de configuração
setup_dependencies.bat
```

### 2. Instalar GLFW3 (Método Recomendado - vcpkg)

```bash
# Instalar vcpkg se não tiver
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install

# Instalar GLFW3
.\vcpkg install glfw3:x64-windows
```

### 3. Instalar GLFW3 (Método Manual)

Se não quiser usar vcpkg:

1. Baixe GLFW3 de: https://www.glfw.org/download.html
2. Extraia para `vendor/glfw3/`
3. Edite `CMakeLists.txt` para apontar para o caminho correto

### 4. Compilar o Projeto

```bash
# Executar script de build
build.bat
```

Ou manualmente:
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### 5. Configurar o Script do FiveM

1. Copie a pasta `fivem_script/` para sua pasta de resources do FiveM
2. Adicione ao `server.cfg`:
```
ensure external_menu_script
```

## ⚙️ Configuração

### Arquivo de Configuração

O arquivo `resources/config.json` será criado automaticamente na primeira execução. Você pode editá-lo para personalizar:

```json
{
  "general": {
    "theme": "dark",
    "language": "pt-BR",
    "auto_connect": true
  },
  "hotkeys": [
    {
      "action": "toggle_menu",
      "key": 290,
      "ctrl": false,
      "shift": false,
      "alt": false
    }
  ]
}
```

### Hotkeys Padrão

- **F1**: Abrir/Fechar menu principal
- **F2**: Toggle janela do jogador
- **F3**: Toggle janela de veículos
- **F4**: Toggle janela de teleporte
- **F5**: Toggle janela visual

## 🎮 Como Usar

### 1. Iniciar o FiveM
- Abra o FiveM e conecte-se a um servidor
- Certifique-se de que o script está carregado no servidor

### 2. Executar o Menu
```bash
cd build/Release
FiveMExternalMenu.exe
```

### 3. Conectar ao FiveM
- O menu tentará conectar automaticamente
- Se não conectar, verifique se o script está rodando no servidor

### 4. Usar as Funcionalidades
- Pressione **F1** para abrir o menu
- Navegue pelas abas para acessar diferentes funcionalidades
- Use os controles para modificar configurações do jogo

## 🔧 Solução de Problemas

### Erro de Compilação

**Problema**: CMake não encontra GLFW3
```
Solution: Certifique-se de que o vcpkg está integrado:
vcpkg integrate install
```

**Problema**: Erro de linking com OpenGL
```
Solution: Instale os drivers de vídeo mais recentes
```

### Erro de Conexão

**Problema**: Menu não conecta ao FiveM
```
Solution: 
1. Verifique se o script está carregado no servidor
2. Execute o FiveM como administrador
3. Verifique se não há firewall bloqueando
```

**Problema**: Named pipe não encontrado
```
Solution:
1. Reinicie o FiveM
2. Certifique-se de que está em um servidor, não no menu principal
3. Verifique se o script está na pasta de resources
```

### Problemas de Performance

**Problema**: Menu lento ou travando
```
Solution:
1. Feche outros programas pesados
2. Reduza as configurações gráficas do FiveM
3. Execute como administrador
```

## 📁 Estrutura de Arquivos

```
build/Release/
├── FiveMExternalMenu.exe    # Executável principal
├── resources/
│   ├── config.json         # Configurações
│   └── fonts/              # Fontes personalizadas
└── logs/                   # Logs de execução
```

## 🛡️ Segurança

### Antivírus
Alguns antivírus podem detectar o programa como suspeito. Isso é normal para ferramentas de modificação de jogos. Adicione uma exceção se necessário.

### Uso Responsável
- Use apenas em servidores privados ou para testes
- Não use em servidores públicos para evitar banimentos
- Respeite as regras do servidor

## 📞 Suporte

### Logs
Os logs são salvos em `logs/external_menu.log`. Inclua este arquivo ao reportar problemas.

### Problemas Comuns
1. **Menu não abre**: Verifique se está executando como administrador
2. **Comandos não funcionam**: Verifique a conexão com o FiveM
3. **Crash ao iniciar**: Verifique se todas as DLLs estão presentes

### Reportar Bugs
Ao reportar problemas, inclua:
- Versão do Windows
- Versão do FiveM
- Arquivo de log
- Passos para reproduzir o problema

## 🔄 Atualizações

Para atualizar o menu:
1. Baixe a nova versão
2. Substitua o executável
3. Mantenha o arquivo `config.json` para preservar suas configurações

## ⚠️ Aviso Legal

Este software é fornecido apenas para fins educacionais e de teste. O uso em servidores públicos pode resultar em banimento. Use por sua própria conta e risco.
