#include "MenuManager.hpp"
#include "windows/PlayerWindow.hpp"
#include "windows/VehicleWindow.hpp"
#include "windows/TeleportWindow.hpp"
#include "windows/VisualWindow.hpp"

#include <GL/glew.h>
#include <GLFW/glfw3.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>

#include <iostream>

namespace FiveMMenu {

    MenuManager::MenuManager() 
        : m_isVisible(false)
        , m_shouldClose(false)
        , m_glfwWindow(nullptr) {
        
        m_communicator = std::make_unique<FiveMCommunicator>();
        m_config = std::make_unique<Config>();
    }

    MenuManager::~MenuManager() {
        Shutdown();
    }

    bool MenuManager::Initialize() {
        // Inicializar GLFW
        if (!glfwInit()) {
            std::cerr << "Erro: Falha ao inicializar GLFW!" << std::endl;
            return false;
        }

        // Configurar GLFW
        glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
        glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
        glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
        glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);
        glfwWindowHint(GLFW_DECORATED, GLFW_TRUE);

        // Criar janela
        m_glfwWindow = glfwCreateWindow(WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_TITLE, nullptr, nullptr);
        if (!m_glfwWindow) {
            std::cerr << "Erro: Falha ao criar janela GLFW!" << std::endl;
            glfwTerminate();
            return false;
        }

        glfwMakeContextCurrent(m_glfwWindow);
        glfwSwapInterval(1); // VSync

        // Inicializar GLEW
        if (glewInit() != GLEW_OK) {
            std::cerr << "Erro: Falha ao inicializar GLEW!" << std::endl;
            return false;
        }

        // Configurar callbacks
        glfwSetWindowUserPointer(m_glfwWindow, this);
        glfwSetKeyCallback(m_glfwWindow, [](GLFWwindow* window, int key, int scancode, int action, int mods) {
            auto* manager = static_cast<MenuManager*>(glfwGetWindowUserPointer(window));
            if (key == GLFW_KEY_F1 && action == GLFW_PRESS) {
                manager->ToggleVisibility();
            }
        });

        // Inicializar Dear ImGui
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO();
        io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
        io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
        io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable;

        // Configurar estilo
        SetupImGuiStyle();

        // Inicializar backends
        ImGui_ImplGlfw_InitForOpenGL(m_glfwWindow, true);
        ImGui_ImplOpenGL3_Init("#version 330");

        // Carregar fontes
        LoadFonts();

        // Carregar configurações
        m_config->Load();

        // Inicializar comunicador
        if (!m_communicator->Initialize()) {
            std::cout << "Aviso: Não foi possível conectar ao FiveM. Tentando reconectar..." << std::endl;
        }

        // Registrar janelas
        RegisterWindow(std::make_unique<PlayerWindow>());
        RegisterWindow(std::make_unique<VehicleWindow>());
        RegisterWindow(std::make_unique<TeleportWindow>());
        RegisterWindow(std::make_unique<VisualWindow>());

        return true;
    }

    void MenuManager::Run() {
        while (!glfwWindowShouldClose(m_glfwWindow) && !m_shouldClose) {
            glfwPollEvents();

            // Iniciar frame do ImGui
            ImGui_ImplOpenGL3_NewFrame();
            ImGui_ImplGlfw_NewFrame();
            ImGui::NewFrame();

            // Processar input
            HandleInput();

            // Renderizar interface se visível
            if (m_isVisible) {
                RenderMenuBar();
                RenderWindows();
            }

            // Atualizar janelas
            for (auto& [name, window] : m_windows) {
                window->Update();
            }

            // Renderizar
            ImGui::Render();
            int display_w, display_h;
            glfwGetFramebufferSize(m_glfwWindow, &display_w, &display_h);
            glViewport(0, 0, display_w, display_h);
            glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
            glClear(GL_COLOR_BUFFER_BIT);
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

            // Multi-viewport
            ImGuiIO& io = ImGui::GetIO();
            if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable) {
                GLFWwindow* backup_current_context = glfwGetCurrentContext();
                ImGui::UpdatePlatformWindows();
                ImGui::RenderPlatformWindowsDefault();
                glfwMakeContextCurrent(backup_current_context);
            }

            glfwSwapBuffers(m_glfwWindow);
        }
    }

    void MenuManager::Shutdown() {
        // Salvar configurações
        if (m_config) {
            m_config->Save();
        }

        // Shutdown comunicador
        if (m_communicator) {
            m_communicator->Shutdown();
        }

        // Limpar janelas
        m_windows.clear();

        // Cleanup ImGui
        ImGui_ImplOpenGL3_Shutdown();
        ImGui_ImplGlfw_Shutdown();
        ImGui::DestroyContext();

        // Cleanup GLFW
        if (m_glfwWindow) {
            glfwDestroyWindow(m_glfwWindow);
        }
        glfwTerminate();
    }

    void MenuManager::RegisterWindow(std::unique_ptr<Window> window) {
        window->SetMenuManager(this);
        m_windows[window->GetName()] = std::move(window);
    }

    void MenuManager::ShowWindow(const std::string& name) {
        auto it = m_windows.find(name);
        if (it != m_windows.end()) {
            it->second->SetVisible(true);
        }
    }

    void MenuManager::HideWindow(const std::string& name) {
        auto it = m_windows.find(name);
        if (it != m_windows.end()) {
            it->second->SetVisible(false);
        }
    }

    void MenuManager::ToggleWindow(const std::string& name) {
        auto it = m_windows.find(name);
        if (it != m_windows.end()) {
            it->second->Toggle();
        }
    }

    void MenuManager::SetupImGuiStyle() {
        ImGuiStyle& style = ImGui::GetStyle();

        // Cores do tema dark profissional
        ImVec4* colors = style.Colors;
        colors[ImGuiCol_Text]                   = ImVec4(1.00f, 1.00f, 1.00f, 1.00f);
        colors[ImGuiCol_TextDisabled]           = ImVec4(0.50f, 0.50f, 0.50f, 1.00f);
        colors[ImGuiCol_WindowBg]               = ImVec4(0.10f, 0.10f, 0.10f, 0.94f);
        colors[ImGuiCol_ChildBg]                = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_PopupBg]                = ImVec4(0.19f, 0.19f, 0.19f, 0.92f);
        colors[ImGuiCol_Border]                 = ImVec4(0.19f, 0.19f, 0.19f, 0.29f);
        colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.24f);
        colors[ImGuiCol_FrameBg]                = ImVec4(0.05f, 0.05f, 0.05f, 0.54f);
        colors[ImGuiCol_FrameBgHovered]         = ImVec4(0.19f, 0.19f, 0.19f, 0.54f);
        colors[ImGuiCol_FrameBgActive]          = ImVec4(0.20f, 0.22f, 0.23f, 1.00f);
        colors[ImGuiCol_TitleBg]                = ImVec4(0.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_TitleBgActive]          = ImVec4(0.06f, 0.06f, 0.06f, 1.00f);
        colors[ImGuiCol_TitleBgCollapsed]       = ImVec4(0.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_MenuBarBg]              = ImVec4(0.14f, 0.14f, 0.14f, 1.00f);
        colors[ImGuiCol_ScrollbarBg]            = ImVec4(0.05f, 0.05f, 0.05f, 0.54f);
        colors[ImGuiCol_ScrollbarGrab]          = ImVec4(0.34f, 0.34f, 0.34f, 0.54f);
        colors[ImGuiCol_ScrollbarGrabHovered]   = ImVec4(0.40f, 0.40f, 0.40f, 0.54f);
        colors[ImGuiCol_ScrollbarGrabActive]    = ImVec4(0.56f, 0.56f, 0.56f, 0.54f);
        colors[ImGuiCol_CheckMark]              = ImVec4(0.33f, 0.67f, 0.86f, 1.00f);
        colors[ImGuiCol_SliderGrab]             = ImVec4(0.34f, 0.34f, 0.34f, 0.54f);
        colors[ImGuiCol_SliderGrabActive]       = ImVec4(0.56f, 0.56f, 0.56f, 0.54f);
        colors[ImGuiCol_Button]                 = ImVec4(0.05f, 0.05f, 0.05f, 0.54f);
        colors[ImGuiCol_ButtonHovered]          = ImVec4(0.19f, 0.19f, 0.19f, 0.54f);
        colors[ImGuiCol_ButtonActive]           = ImVec4(0.20f, 0.22f, 0.23f, 1.00f);
        colors[ImGuiCol_Header]                 = ImVec4(0.00f, 0.00f, 0.00f, 0.52f);
        colors[ImGuiCol_HeaderHovered]          = ImVec4(0.00f, 0.00f, 0.00f, 0.36f);
        colors[ImGuiCol_HeaderActive]           = ImVec4(0.20f, 0.22f, 0.23f, 0.33f);
        colors[ImGuiCol_Separator]              = ImVec4(0.28f, 0.28f, 0.28f, 0.29f);
        colors[ImGuiCol_SeparatorHovered]       = ImVec4(0.44f, 0.44f, 0.44f, 0.29f);
        colors[ImGuiCol_SeparatorActive]        = ImVec4(0.40f, 0.44f, 0.47f, 1.00f);
        colors[ImGuiCol_ResizeGrip]             = ImVec4(0.28f, 0.28f, 0.28f, 0.29f);
        colors[ImGuiCol_ResizeGripHovered]      = ImVec4(0.44f, 0.44f, 0.44f, 0.29f);
        colors[ImGuiCol_ResizeGripActive]       = ImVec4(0.40f, 0.44f, 0.47f, 1.00f);
        colors[ImGuiCol_Tab]                    = ImVec4(0.00f, 0.00f, 0.00f, 0.52f);
        colors[ImGuiCol_TabHovered]             = ImVec4(0.14f, 0.14f, 0.14f, 1.00f);
        colors[ImGuiCol_TabActive]              = ImVec4(0.20f, 0.20f, 0.20f, 0.36f);
        colors[ImGuiCol_TabUnfocused]           = ImVec4(0.00f, 0.00f, 0.00f, 0.52f);
        colors[ImGuiCol_TabUnfocusedActive]     = ImVec4(0.14f, 0.14f, 0.14f, 1.00f);
        colors[ImGuiCol_DockingPreview]         = ImVec4(0.33f, 0.67f, 0.86f, 1.00f);
        colors[ImGuiCol_DockingEmptyBg]         = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_PlotLines]              = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_PlotLinesHovered]       = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_PlotHistogram]          = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_PlotHistogramHovered]   = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_TableHeaderBg]          = ImVec4(0.00f, 0.00f, 0.00f, 0.52f);
        colors[ImGuiCol_TableBorderStrong]      = ImVec4(0.00f, 0.00f, 0.00f, 0.52f);
        colors[ImGuiCol_TableBorderLight]       = ImVec4(0.28f, 0.28f, 0.28f, 0.29f);
        colors[ImGuiCol_TableRowBg]             = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_TableRowBgAlt]          = ImVec4(1.00f, 1.00f, 1.00f, 0.06f);
        colors[ImGuiCol_TextSelectedBg]         = ImVec4(0.20f, 0.22f, 0.23f, 1.00f);
        colors[ImGuiCol_DragDropTarget]         = ImVec4(0.33f, 0.67f, 0.86f, 1.00f);
        colors[ImGuiCol_NavHighlight]           = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);
        colors[ImGuiCol_NavWindowingHighlight]  = ImVec4(1.00f, 0.00f, 0.00f, 0.70f);
        colors[ImGuiCol_NavWindowingDimBg]      = ImVec4(1.00f, 0.00f, 0.00f, 0.20f);
        colors[ImGuiCol_ModalWindowDimBg]       = ImVec4(1.00f, 0.00f, 0.00f, 0.35f);

        // Configurações de estilo
        style.WindowPadding                     = ImVec2(8.00f, 8.00f);
        style.FramePadding                      = ImVec2(5.00f, 2.00f);
        style.CellPadding                       = ImVec2(6.00f, 6.00f);
        style.ItemSpacing                       = ImVec2(6.00f, 6.00f);
        style.ItemInnerSpacing                  = ImVec2(6.00f, 6.00f);
        style.TouchExtraPadding                 = ImVec2(0.00f, 0.00f);
        style.IndentSpacing                     = 25;
        style.ScrollbarSize                     = 15;
        style.GrabMinSize                       = 10;
        style.WindowBorderSize                  = 1;
        style.ChildBorderSize                   = 1;
        style.PopupBorderSize                   = 1;
        style.FrameBorderSize                   = 1;
        style.TabBorderSize                     = 1;
        style.WindowRounding                    = 7;
        style.ChildRounding                     = 4;
        style.FrameRounding                     = 3;
        style.PopupRounding                     = 4;
        style.ScrollbarRounding                 = 9;
        style.GrabRounding                      = 3;
        style.LogSliderDeadzone                 = 4;
        style.TabRounding                       = 4;
    }

    void MenuManager::HandleInput() {
        // Processar hotkeys da configuração
        for (const auto& hotkey : m_config->GetHotkeys()) {
            if (m_config->IsHotkeyPressed(hotkey.action)) {
                if (hotkey.action == "toggle_menu") {
                    ToggleVisibility();
                } else if (hotkey.action == "toggle_player") {
                    ToggleWindow("Player");
                } else if (hotkey.action == "toggle_vehicle") {
                    ToggleWindow("Vehicle");
                } else if (hotkey.action == "toggle_teleport") {
                    ToggleWindow("Teleport");
                } else if (hotkey.action == "toggle_visual") {
                    ToggleWindow("Visual");
                }
            }
        }
    }

    void MenuManager::RenderMenuBar() {
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("Janelas")) {
                for (auto& [name, window] : m_windows) {
                    bool visible = window->IsVisible();
                    if (ImGui::MenuItem(window->GetTitle().c_str(), nullptr, &visible)) {
                        window->SetVisible(visible);
                    }
                }
                ImGui::EndMenu();
            }

            if (ImGui::BeginMenu("Configurações")) {
                if (ImGui::MenuItem("Salvar Configuração")) {
                    m_config->Save();
                }
                if (ImGui::MenuItem("Carregar Configuração")) {
                    m_config->Load();
                }
                ImGui::Separator();
                if (ImGui::MenuItem("Sair")) {
                    m_shouldClose = true;
                }
                ImGui::EndMenu();
            }

            // Status da conexão
            ImGui::SameLine(ImGui::GetWindowWidth() - 200);
            if (m_communicator->IsConnected()) {
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Conectado ao FiveM");
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Desconectado");
            }

            ImGui::EndMainMenuBar();
        }
    }

    void MenuManager::RenderWindows() {
        for (auto& [name, window] : m_windows) {
            if (window->IsVisible()) {
                window->Render();
            }
        }
    }

    void MenuManager::LoadFonts() {
        ImGuiIO& io = ImGui::GetIO();

        // Fonte padrão
        io.Fonts->AddFontFromFileTTF("resources/fonts/Roboto-Regular.ttf", 16.0f);

        // Fonte para títulos
        io.Fonts->AddFontFromFileTTF("resources/fonts/Roboto-Bold.ttf", 18.0f);

        // Fonte para ícones (se disponível)
        // io.Fonts->AddFontFromFileTTF("resources/fonts/FontAwesome.ttf", 16.0f);
    }

} // namespace FiveMMenu
