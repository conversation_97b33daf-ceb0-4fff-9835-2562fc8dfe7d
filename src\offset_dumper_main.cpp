#include <iostream>
#include <memory>
#include <windows.h>

#include "ProcessManager.hpp"
#include "SimpleOffsetDumper.cpp"  // Include the simple implementation

using namespace FiveMCheat;

int main() {
    // Set console title
    SetConsoleTitleA("FiveM Offset Dumper v1.0");
    
    std::cout << "=== FiveM Offset Dumper ===" << std::endl;
    std::cout << "Baseado no Guided Hacking Offset Dumper" << std::endl;
    std::cout << "=================================" << std::endl << std::endl;
    
    try {
        // Create offset dumper
        auto offsetDumper = std::make_shared<SimpleOffsetDumper>();

        // Try to attach to FiveM process
        std::cout << "[~] Procurando processo FiveM..." << std::endl;
        if (!offsetDumper->AttachToProcess("FiveM.exe")) {
            std::cerr << "[-] Falha ao anexar ao FiveM.exe" << std::endl;
            std::cerr << "[!] Certifique-se de que o FiveM está rodando" << std::endl;
            std::cout << std::endl << "Pressione Enter para sair...";
            std::cin.get();
            return 1;
        }

        std::cout << "[+] Anexado ao processo FiveM com sucesso!" << std::endl;
        std::cout << "[+] PID: " << offsetDumper->GetProcessId() << std::endl;
        std::cout << "[+] Base Address: 0x" << std::hex << offsetDumper->GetBaseAddress() << std::dec << std::endl;
        std::cout << std::endl;
        
        // Load signature configuration
        std::cout << "[~] Carregando configuração de assinaturas..." << std::endl;
        if (!offsetDumper->LoadConfig("configs/fivem_signatures.json")) {
            std::cerr << "[-] Falha ao carregar configs/fivem_signatures.json" << std::endl;
            std::cerr << "[!] Certifique-se de que o arquivo existe" << std::endl;
            std::cout << std::endl << "Pressione Enter para sair...";
            std::cin.get();
            return 1;
        }
        
        std::cout << "[+] Configuração carregada com sucesso!" << std::endl;
        std::cout << "[+] Assinaturas carregadas: " << offsetDumper->GetSignatureCount() << std::endl;
        std::cout << std::endl;
        
        // Start dumping offsets
        std::cout << "[~] Iniciando dump de offsets..." << std::endl;
        std::cout << "=================================" << std::endl;
        
        if (!offsetDumper->DumpOffsets()) {
            std::cerr << "[-] Falha no dump de offsets: " << offsetDumper->GetLastError() << std::endl;
            std::cout << std::endl << "Pressione Enter para sair...";
            std::cin.get();
            return 1;
        }
        
        std::cout << "=================================" << std::endl;
        std::cout << "[+] Dump de offsets concluído!" << std::endl;
        std::cout << std::endl;
        
        // Get results
        auto results = offsetDumper->GetResults();
        int foundCount = 0;
        int totalCount = results.size();

        for (const auto& result : results) {
            if (result.found) {
                foundCount++;
            }
        }
        
        std::cout << "=== RESULTADOS ===" << std::endl;
        std::cout << "Total de assinaturas: " << totalCount << std::endl;
        std::cout << "Encontradas: " << foundCount << std::endl;
        std::cout << "Falharam: " << (totalCount - foundCount) << std::endl;
        std::cout << "Taxa de sucesso: " << (foundCount * 100.0 / totalCount) << "%" << std::endl;
        std::cout << std::endl;
        
        // Show detailed results
        std::cout << "=== OFFSETS ENCONTRADOS ===" << std::endl;
        for (const auto& result : results) {
            if (result.found) {
                std::cout << "[+] " << result.name << ": 0x" << std::hex << result.address << std::dec << std::endl;
            } else {
                std::cout << "[-] " << result.name << ": FALHOU" << std::endl;
            }
        }
        std::cout << std::endl;
        
        // Save results to different formats
        std::cout << "[~] Salvando resultados..." << std::endl;
        
        // Save as C++ header
        if (offsetDumper->SaveAsHeader("offsets/FiveMOffsets.hpp")) {
            std::cout << "[+] Header C++ salvo: offsets/FiveMOffsets.hpp" << std::endl;
        }
        
        // Save as Cheat Engine table
        if (offsetDumper->SaveAsCheatEngineTable("offsets/FiveMOffsets.ct")) {
            std::cout << "[+] Tabela Cheat Engine salva: offsets/FiveMOffsets.ct" << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "[+] Offset dumper executado com sucesso!" << std::endl;
        std::cout << "[!] Os offsets foram salvos em múltiplos formatos na pasta 'offsets/'" << std::endl;
        std::cout << "[!] Você pode agora usar estes offsets no cheat principal" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[-] Erro fatal: " << e.what() << std::endl;
        std::cout << std::endl << "Pressione Enter para sair...";
        std::cin.get();
        return 1;
    }
    
    std::cout << std::endl << "Pressione Enter para sair...";
    std::cin.get();
    return 0;
}
