@echo off
echo ================================
echo FiveM Offset Dumper Build Script
echo ================================
echo.

REM Verificar se o Visual Studio está instalado
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo [!] Visual Studio não encontrado. Tentando configurar ambiente...
    
    REM Tentar encontrar vcvarsall.bat
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvarsall.bat" x64
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvarsall.bat" x64
    ) else (
        echo [-] Visual Studio 2022 não encontrado!
        echo [!] Instale o Visual Studio 2022 com C++ workload
        pause
        exit /b 1
    )
)

REM Verificar se o CMake está instalado
where cmake >nul 2>nul
if %errorlevel% neq 0 (
    echo [-] CMake não encontrado!
    echo [!] Instale o CMake: https://cmake.org/download/
    pause
    exit /b 1
)

echo [+] Ferramentas encontradas com sucesso!
echo.

REM Criar diretório de build
if not exist "build_dumper" (
    mkdir build_dumper
    echo [+] Diretório build_dumper criado
) else (
    echo [+] Usando diretório build_dumper existente
)

cd build_dumper

echo [~] Configurando projeto com CMake...
copy ..\CMakeLists_OffsetDumper.txt CMakeLists.txt
cmake . -G "Visual Studio 17 2022" -A x64
if %errorlevel% neq 0 (
    echo [-] Falha na configuração do CMake!
    cd ..
    pause
    exit /b 1
)

echo [+] Configuração concluída!
echo.

echo [~] Compilando projeto...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo [-] Falha na compilação!
    cd ..
    pause
    exit /b 1
)

echo [+] Compilação concluída com sucesso!
echo.

REM Verificar se o executável foi criado
if exist "Release\FiveMOffsetDumper.exe" (
    echo [+] Executável criado: build_dumper\Release\FiveMOffsetDumper.exe
    echo.
    echo ================================
    echo COMPILAÇÃO CONCLUÍDA COM SUCESSO!
    echo ================================
    echo.
    echo Para usar o offset dumper:
    echo 1. Abra o FiveM
    echo 2. Execute: build_dumper\Release\FiveMOffsetDumper.exe
    echo.
    echo Os offsets serão salvos na pasta 'offsets\'
    echo.
) else (
    echo [-] Executável não encontrado!
    echo [!] Verifique os erros de compilação acima
)

cd ..
pause
