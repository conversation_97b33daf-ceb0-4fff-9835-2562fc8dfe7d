#include "windows/PlayerWindow.hpp"
#include "MenuManager.hpp"
#include <imgui.h>

namespace FiveMMenu {

    PlayerWindow::PlayerWindow() 
        : Window("Player", "Configurações do Jogador") {
        SetSize(500, 600);
        SetFlags(ImGuiWindowFlags_NoCollapse);
    }

    void PlayerWindow::Render() {
        if (!m_isVisible) return;

        ImGui::SetNextWindowSize(m_size, ImGuiCond_FirstUseEver);
        ImGui::SetNextWindowPos(m_position, ImGuiCond_FirstUseEver);

        if (ImGui::Begin(m_title.c_str(), &m_isVisible, m_flags)) {
            if (ImGui::BeginTabBar("PlayerTabs")) {
                if (ImGui::BeginTabItem("Saúde & Status")) {
                    RenderHealthSection();
                    ImGui::EndTabItem();
                }
                
                if (ImGui::BeginTabItem("Movimento")) {
                    RenderMovementSection();
                    ImGui::EndTabItem();
                }
                
                if (ImGui::BeginTabItem("Dinheiro")) {
                    RenderMoneySection();
                    ImGui::EndTabItem();
                }
                
                if (ImGui::BeginTabItem("Armas")) {
                    RenderWeaponsSection();
                    ImGui::EndTabItem();
                }
                
                if (ImGui::BeginTabItem("Diversos")) {
                    RenderMiscSection();
                    ImGui::EndTabItem();
                }
                
                ImGui::EndTabBar();
            }
        }
        ImGui::End();
    }

    void PlayerWindow::Update() {
        // Atualizar estado baseado na configuração
        if (m_menuManager) {
            auto& config = m_menuManager->GetConfig();
            m_godMode = config.player.godMode;
            m_invisible = config.player.invisible;
            m_noClip = config.player.noClip;
            m_walkSpeed = config.player.walkSpeed;
            m_runSpeed = config.player.runSpeed;
        }
    }

    void PlayerWindow::RenderHealthSection() {
        ImGui::Text("Controle de Saúde e Armadura");
        ImGui::Separator();
        Spacing();

        // Saúde
        ImGui::Text("Saúde:");
        ImGui::SliderInt("##Health", &m_healthValue, 0, 100, "%d%%");
        ImGui::SameLine();
        if (ColoredButton("Definir Saúde", ImVec4(0.2f, 0.8f, 0.2f, 1.0f))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetPlayerHealth(m_healthValue);
            }
        }

        Spacing();

        // Armadura
        ImGui::Text("Armadura:");
        ImGui::SliderInt("##Armor", &m_armorValue, 0, 100, "%d%%");
        ImGui::SameLine();
        if (ColoredButton("Definir Armadura", ImVec4(0.2f, 0.2f, 0.8f, 1.0f))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetPlayerArmor(m_armorValue);
            }
        }

        Spacing(2);

        // Botões rápidos
        ImGui::Text("Ações Rápidas:");
        if (ColoredButton("Vida Completa", ImVec4(0.0f, 0.8f, 0.0f, 1.0f), ImVec2(120, 30))) {
            m_healthValue = 100;
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetPlayerHealth(100);
            }
        }
        ImGui::SameLine();
        if (ColoredButton("Armadura Completa", ImVec4(0.0f, 0.0f, 0.8f, 1.0f), ImVec2(120, 30))) {
            m_armorValue = 100;
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetPlayerArmor(100);
            }
        }

        Spacing();

        if (ColoredButton("Curar Tudo", ImVec4(0.8f, 0.8f, 0.0f, 1.0f), ImVec2(250, 30))) {
            m_healthValue = 100;
            m_armorValue = 100;
            if (m_menuManager) {
                auto& comm = m_menuManager->GetCommunicator();
                comm.SetPlayerHealth(100);
                comm.SetPlayerArmor(100);
            }
        }
    }

    void PlayerWindow::RenderMovementSection() {
        ImGui::Text("Configurações de Movimento");
        ImGui::Separator();
        Spacing();

        // Velocidades
        ImGui::Text("Velocidade de Caminhada:");
        if (ImGui::SliderFloat("##WalkSpeed", &m_walkSpeed, 0.1f, 5.0f, "%.1fx")) {
            if (m_menuManager) {
                m_menuManager->GetConfig().player.walkSpeed = m_walkSpeed;
            }
        }

        ImGui::Text("Velocidade de Corrida:");
        if (ImGui::SliderFloat("##RunSpeed", &m_runSpeed, 0.1f, 10.0f, "%.1fx")) {
            if (m_menuManager) {
                m_menuManager->GetConfig().player.runSpeed = m_runSpeed;
            }
        }

        Spacing(2);

        // Toggles de movimento
        ImGui::Text("Habilidades Especiais:");
        
        if (ImGui::Checkbox("Stamina Infinita", &m_infiniteStamina)) {
            // Implementar comando
        }
        HelpMarker("Nunca fica cansado ao correr");

        if (ImGui::Checkbox("Corrida Rápida", &m_fastRun)) {
            // Implementar comando
        }
        HelpMarker("Aumenta significativamente a velocidade de corrida");

        if (ImGui::Checkbox("Super Pulo", &m_superJump)) {
            // Implementar comando
        }
        HelpMarker("Permite pulos muito mais altos");

        Spacing(2);

        // Botões de reset
        if (ColoredButton("Resetar Velocidades", ImVec4(0.8f, 0.4f, 0.0f, 1.0f))) {
            m_walkSpeed = 1.0f;
            m_runSpeed = 1.0f;
            if (m_menuManager) {
                auto& config = m_menuManager->GetConfig();
                config.player.walkSpeed = 1.0f;
                config.player.runSpeed = 1.0f;
            }
        }
    }

    void PlayerWindow::RenderMoneySection() {
        ImGui::Text("Gerenciamento de Dinheiro");
        ImGui::Separator();
        Spacing();

        ImGui::Text("Quantidade:");
        ImGui::InputInt("##MoneyAmount", &m_moneyAmount, 100, 1000);
        
        Spacing();

        if (ColoredButton("Adicionar Dinheiro", ImVec4(0.0f, 0.8f, 0.0f, 1.0f), ImVec2(150, 30))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(m_moneyAmount);
            }
        }

        Spacing(2);

        // Botões de valores predefinidos
        ImGui::Text("Valores Rápidos:");
        
        if (ColoredButton("$1,000", ImVec4(0.2f, 0.6f, 0.2f, 1.0f), ImVec2(80, 25))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(1000);
            }
        }
        ImGui::SameLine();
        if (ColoredButton("$10,000", ImVec4(0.3f, 0.7f, 0.3f, 1.0f), ImVec2(80, 25))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(10000);
            }
        }
        ImGui::SameLine();
        if (ColoredButton("$100,000", ImVec4(0.4f, 0.8f, 0.4f, 1.0f), ImVec2(80, 25))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(100000);
            }
        }

        if (ColoredButton("$1,000,000", ImVec4(0.5f, 0.9f, 0.5f, 1.0f), ImVec2(80, 25))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(1000000);
            }
        }
        ImGui::SameLine();
        if (ColoredButton("$10,000,000", ImVec4(0.6f, 1.0f, 0.6f, 1.0f), ImVec2(80, 25))) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().GiveMoney(10000000);
            }
        }
    }

    void PlayerWindow::RenderWeaponsSection() {
        ImGui::Text("Gerenciamento de Armas");
        ImGui::Separator();
        Spacing();

        ImGui::Text("Selecionar Arma:");
        if (ImGui::BeginCombo("##WeaponSelect", m_weapons[m_selectedWeapon].first.c_str())) {
            for (int i = 0; i < m_weapons.size(); ++i) {
                bool isSelected = (m_selectedWeapon == i);
                if (ImGui::Selectable(m_weapons[i].first.c_str(), isSelected)) {
                    m_selectedWeapon = i;
                }
                if (isSelected) {
                    ImGui::SetItemDefaultFocus();
                }
            }
            ImGui::EndCombo();
        }

        ImGui::Text("Munição:");
        ImGui::InputInt("##WeaponAmmo", &m_weaponAmmo, 50, 250);

        Spacing();

        if (ColoredButton("Dar Arma", ImVec4(0.8f, 0.2f, 0.2f, 1.0f), ImVec2(100, 30))) {
            // Implementar comando para dar arma
        }
        ImGui::SameLine();
        if (ColoredButton("Todas as Armas", ImVec4(0.8f, 0.4f, 0.0f, 1.0f), ImVec2(120, 30))) {
            // Implementar comando para dar todas as armas
        }

        Spacing(2);

        if (ColoredButton("Remover Todas", ImVec4(0.6f, 0.0f, 0.0f, 1.0f), ImVec2(120, 30))) {
            // Implementar comando para remover todas as armas
        }
    }

    void PlayerWindow::RenderMiscSection() {
        ImGui::Text("Configurações Diversas");
        ImGui::Separator();
        Spacing();

        // Modos especiais
        if (ImGui::Checkbox("Modo Deus", &m_godMode)) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetGodMode(m_godMode);
                m_menuManager->GetConfig().player.godMode = m_godMode;
            }
        }
        HelpMarker("Torna o jogador invulnerável a danos");

        if (ImGui::Checkbox("Invisibilidade", &m_invisible)) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetInvisible(m_invisible);
                m_menuManager->GetConfig().player.invisible = m_invisible;
            }
        }
        HelpMarker("Torna o jogador invisível para outros jogadores");

        if (ImGui::Checkbox("NoClip", &m_noClip)) {
            if (m_menuManager) {
                m_menuManager->GetCommunicator().SetNoClip(m_noClip);
                m_menuManager->GetConfig().player.noClip = m_noClip;
            }
        }
        HelpMarker("Permite voar através de objetos sólidos");

        Spacing(2);

        // Status atual
        ImGui::Text("Status Atual:");
        ImGui::TextColored(m_godMode ? ImVec4(0.0f, 1.0f, 0.0f, 1.0f) : ImVec4(1.0f, 0.0f, 0.0f, 1.0f), 
                          "Modo Deus: %s", m_godMode ? "ATIVO" : "INATIVO");
        ImGui::TextColored(m_invisible ? ImVec4(0.0f, 1.0f, 0.0f, 1.0f) : ImVec4(1.0f, 0.0f, 0.0f, 1.0f), 
                          "Invisível: %s", m_invisible ? "ATIVO" : "INATIVO");
        ImGui::TextColored(m_noClip ? ImVec4(0.0f, 1.0f, 0.0f, 1.0f) : ImVec4(1.0f, 0.0f, 0.0f, 1.0f), 
                          "NoClip: %s", m_noClip ? "ATIVO" : "INATIVO");
    }

} // namespace FiveMMenu
