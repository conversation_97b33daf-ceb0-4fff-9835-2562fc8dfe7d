{"gameName": "FiveM", "gameExecutable": "FiveM.exe", "outputDirectory": "./dumps/fivem/", "headerFileName": "fivem_offsets.hpp", "generateHeader": true, "generateCheatTable": true, "generateReClassFile": true, "relativeByDefault": true, "fileOnly": false, "verboseOutput": true, "maxRetries": 3, "retryDelay": 1000, "additionalModules": ["CitizenFX.exe", "CitizenGame.dll", "GTA5.exe"], "signatures": [{"name": "WorldPtr", "pattern": "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9", "mask": "xxx???x?????xxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Pointer to the game world structure", "required": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "pattern": "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9 74 52", "mask": "xxx???x?????xxxxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Player manager structure", "required": true}, {"name": "EntityList", "pattern": "4C 8B 0D ? ? ? ? 44 8B C1 49 8B 41 08", "mask": "xxx???xxxxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Entity list pointer", "required": true}, {"name": "LocalPlayer", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 50 ?", "mask": "xxx???xxxx?xxxxxx?", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Local player pointer", "required": true}, {"name": "ViewMatrix", "pattern": "0F 29 05 ? ? ? ? 0F 28 35 ? ? ? ? 0F 29 0D", "mask": "xxx???xxxx???xxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "View matrix for world to screen conversion", "required": true}, {"name": "CameraManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 06 48 8B 01 FF 50 08", "mask": "xxx???xxxxxxxxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Camera manager structure", "required": true}, {"name": "VehicleManager", "pattern": "48 8B 05 ? ? ? ? 48 8B 50 08 48 85 D2 74 5A", "mask": "xxx???xxxxxxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Vehicle manager structure", "required": true}, {"name": "WeaponManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 49 ? 48 85 C9", "mask": "xxx???xxxx?xxx?xxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Weapon manager structure", "required": false}, {"name": "BlipManager", "pattern": "4C 8B 05 ? ? ? ? 49 8B 40 ? 48 85 C0 74 ?", "mask": "xxx???xxx?xxxx?", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Blip/radar manager", "required": false}, {"name": "GameState", "pattern": "83 3D ? ? ? ? ? 75 ? 48 8B 0D ? ? ? ?", "mask": "xx????x?xxxx???", "module": "", "isRelative": true, "operandLocation": 2, "operandLength": 4, "additionalOffset": 0, "description": "Game state information", "required": false}, {"name": "NetworkManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? E8 ? ? ? ? 84 C0", "mask": "xxx???xxxx?x???xx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Network manager for multiplayer", "required": false}, {"name": "InputManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 0F 84 ? ? ? ? 48 8B 01", "mask": "xxx???xxxxx???xxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Input manager for controls", "required": false}, {"name": "ScriptEngine", "pattern": "48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9 74 ?", "mask": "xxx???xxx???xxxx?", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Script engine for native calls", "required": false}, {"name": "ModelInfo", "pattern": "48 8B 05 ? ? ? ? 48 63 D1 48 8D 14 D2 48 8B 04 D0", "mask": "xxx???xxxxxxxxxxxxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Model information array", "required": false}, {"name": "WeatherManager", "pattern": "48 8B 0D ? ? ? ? F3 0F 10 05 ? ? ? ? F3 0F 11 41", "mask": "xxx???xxxx???xxxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Weather system manager", "required": false}, {"name": "TimeManager", "pattern": "8B 05 ? ? ? ? 89 44 24 ? 8B 05 ? ? ? ?", "mask": "xx???xxx?xx???", "module": "", "isRelative": true, "operandLocation": 2, "operandLength": 4, "additionalOffset": 0, "description": "Time and clock manager", "required": false}, {"name": "AudioManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 90 ? ? ? ?", "mask": "xxx???xxxx?xxxxx???", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Audio system manager", "required": false}, {"name": "PhysicsWorld", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? E8 ? ? ? ? 48 8B D8", "mask": "xxx???xxxx?x???xxx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Physics world simulation", "required": false}, {"name": "RenderManager", "pattern": "48 8B 0D ? ? ? ? 48 85 C9 74 ? 48 8B 01 FF 50 ? 84 C0", "mask": "xxx???xxxx?xxxxxx?xx", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Rendering system manager", "required": false}, {"name": "MemoryPool", "pattern": "48 8B 05 ? ? ? ? 48 8B 0C C8 48 85 C9 74 ?", "mask": "xxx???xxxxxxxxx?", "module": "", "isRelative": true, "operandLocation": 3, "operandLength": 4, "additionalOffset": 0, "description": "Memory pool for entities", "required": false}]}