#pragma once

#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <unordered_map>

#include "Window.hpp"
#include "FiveMCommunicator.hpp"
#include "Config.hpp"

namespace FiveMMenu {

    class MenuManager {
    public:
        MenuManager();
        ~MenuManager();

        bool Initialize();
        void Run();
        void Shutdown();

        // Gerenciamento de janelas
        void RegisterWindow(std::unique_ptr<Window> window);
        void ShowWindow(const std::string& name);
        void HideWindow(const std::string& name);
        void ToggleWindow(const std::string& name);

        // Getters
        FiveMCommunicator& GetCommunicator() { return *m_communicator; }
        Config& GetConfig() { return *m_config; }

        // Estado do menu
        bool IsVisible() const { return m_isVisible; }
        void SetVisible(bool visible) { m_isVisible = visible; }
        void ToggleVisibility() { m_isVisible = !m_isVisible; }

    private:
        void SetupImGuiStyle();
        void HandleInput();
        void RenderMenuBar();
        void RenderWindows();
        void LoadFonts();

        // Membros
        std::unique_ptr<FiveMCommunicator> m_communicator;
        std::unique_ptr<Config> m_config;
        std::unordered_map<std::string, std::unique_ptr<Window>> m_windows;
        
        bool m_isVisible;
        bool m_shouldClose;
        
        // OpenGL/GLFW
        GLFWwindow* m_glfwWindow;
        
        // Configurações da janela
        static constexpr int WINDOW_WIDTH = 1200;
        static constexpr int WINDOW_HEIGHT = 800;
        static constexpr const char* WINDOW_TITLE = "FiveM External Menu v1.0";
    };

} // namespace FiveMMenu
