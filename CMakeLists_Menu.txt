cmake_minimum_required(VERSION 3.16)
project(FiveMCheatMenu)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release for optimization
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Add compiler flags for Windows
if(WIN32)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
    add_compile_definitions(NOMINMAX)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
endif()

# Find DirectX
find_package(DirectX QUIET)

# ImGui source files (we'll need to download these)
set(IMGUI_DIR ${CMAKE_CURRENT_SOURCE_DIR}/vendor/imgui)
set(IMGUI_SOURCES
    ${IMGUI_DIR}/imgui.cpp
    ${IMGUI_DIR}/imgui_demo.cpp
    ${IMGUI_DIR}/imgui_draw.cpp
    ${IMGUI_DIR}/imgui_tables.cpp
    ${IMGUI_DIR}/imgui_widgets.cpp
    ${IMGUI_DIR}/imgui_impl_win32.cpp
    ${IMGUI_DIR}/imgui_impl_dx11.cpp
)

# Menu source files
set(MENU_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ImGuiMenu.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/menu_main.cpp
)

# Create executable
add_executable(FiveMCheatMenu ${MENU_SOURCES} ${IMGUI_SOURCES})

# Include directories
target_include_directories(FiveMCheatMenu PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/vendor/imgui
    ${CMAKE_CURRENT_SOURCE_DIR}/vendor
)

# Link libraries
if(WIN32)
    target_link_libraries(FiveMCheatMenu
        d3d11
        dxgi
        d3dcompiler
        kernel32
        user32
        gdi32
        winspool
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        advapi32
    )
endif()

# Set output directory
set_target_properties(FiveMCheatMenu PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_menu/Release
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_CURRENT_SOURCE_DIR}/build_menu/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/build_menu/Release
)

# Copy any required DLLs or resources
if(WIN32)
    # Add custom command to copy any required files
    add_custom_command(TARGET FiveMCheatMenu POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "Menu compilado com sucesso!"
    )
endif()

# Set startup project for Visual Studio
if(WIN32)
    set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT FiveMCheatMenu)
endif()
