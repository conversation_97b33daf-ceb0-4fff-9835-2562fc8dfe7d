#include <iostream>
#include <memory>

#include "MenuManager.hpp"

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

int main() {
#ifdef _WIN32
    // Configurar console para UTF-8 no Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    
    // Alocar console se necessário (para debug)
    #ifdef DEBUG
    if (AllocConsole()) {
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
        std::cout.clear();
        std::cerr.clear();
        std::cin.clear();
    }
    #endif
#endif

    try {
        // Criar e inicializar o gerenciador de menu
        auto menuManager = std::make_unique<FiveMMenu::MenuManager>();
        
        if (!menuManager->Initialize()) {
            std::cerr << "Erro: Falha ao inicializar o menu!" << std::endl;
            return -1;
        }

        std::cout << "FiveM External Menu v1.0 iniciado com sucesso!" << std::endl;
        std::cout << "Pressione F1 para abrir/fechar o menu" << std::endl;

        // Loop principal
        menuManager->Run();

        // Cleanup
        menuManager->Shutdown();
        
    } catch (const std::exception& e) {
        std::cerr << "Erro fatal: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
