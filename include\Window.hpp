#pragma once

#include <string>
#include <imgui.h>

namespace FiveMMenu {

    class MenuManager;

    class Window {
    public:
        Window(const std::string& name, const std::string& title);
        virtual ~Window() = default;

        virtual void Render() = 0;
        virtual void Update() {}

        // Getters/Setters
        const std::string& GetName() const { return m_name; }
        const std::string& GetTitle() const { return m_title; }
        bool IsVisible() const { return m_isVisible; }
        void SetVisible(bool visible) { m_isVisible = visible; }
        void Toggle() { m_isVisible = !m_isVisible; }

        // Configurações da janela
        void SetSize(float width, float height) { m_size = ImVec2(width, height); }
        void SetPosition(float x, float y) { m_position = ImVec2(x, y); }
        void SetFlags(ImGuiWindowFlags flags) { m_flags = flags; }

        // Referência ao MenuManager
        void SetMenuManager(MenuManager* manager) { m_menuManager = manager; }

    protected:
        std::string m_name;
        std::string m_title;
        bool m_isVisible;
        
        ImVec2 m_size;
        ImVec2 m_position;
        ImGuiWindowFlags m_flags;
        
        MenuManager* m_menuManager;

        // Helpers para UI
        void HelpMarker(const char* desc);
        bool ColoredButton(const char* label, const ImVec4& color, const ImVec2& size = ImVec2(0, 0));
        void Spacing(int count = 1);
    };

} // namespace FiveMMenu
