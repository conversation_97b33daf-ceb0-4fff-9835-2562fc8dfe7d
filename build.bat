@echo off
echo ========================================
echo    FiveM External Menu - Build Script
echo ========================================

:: Criar diretório de build se não existir
if not exist "build" mkdir build
cd build

:: Configurar com CMake
echo Configurando projeto com CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo Erro na configuração do CMake!
    pause
    exit /b 1
)

:: Compilar projeto
echo Compilando projeto...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Erro na compilação!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Compilação concluída com sucesso!
echo ========================================
echo.
echo O executável está em: build\Release\FiveMExternalMenu.exe
echo.

pause
