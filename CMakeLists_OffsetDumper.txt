cmake_minimum_required(VERSION 3.16)
project(FiveMOffsetDumper VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações de build
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Configurações específicas do Windows
if(WIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
endif()

# Incluir nlohmann/json
set(JSON_DIR ${CMAKE_CURRENT_SOURCE_DIR}/vendor/nlohmann)

# Arquivos fonte do offset dumper
set(OFFSET_DUMPER_SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/../src/offset_dumper_main.cpp
)

# Arquivos de cabeçalho
set(OFFSET_DUMPER_HEADERS
)

# Criar executável do offset dumper
add_executable(FiveMOffsetDumper 
    ${OFFSET_DUMPER_SOURCES}
    ${OFFSET_DUMPER_HEADERS}
)

# Diretórios de include
target_include_directories(FiveMOffsetDumper PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../src
    ${JSON_DIR}/include
)

# Definições de compilação
target_compile_definitions(FiveMOffsetDumper PRIVATE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
    _CRT_SECURE_NO_WARNINGS
    UNICODE
    _UNICODE
)

# Linkar bibliotecas do Windows
target_link_libraries(FiveMOffsetDumper
    kernel32
    user32
    psapi
    ntdll
    advapi32
)

# Configurações de debug
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(FiveMOffsetDumper PRIVATE DEBUG=1)
    # Manter console para debug
    set_target_properties(FiveMOffsetDumper PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
else()
    # Console para release também (é um tool de linha de comando)
    set_target_properties(FiveMOffsetDumper PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif()

# Copiar configs para o diretório de build
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../configs")
    file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/../configs" DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
endif()

# Criar diretório de output
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/offsets)

# Configurações de instalação
install(TARGETS FiveMOffsetDumper DESTINATION bin)
install(DIRECTORY configs DESTINATION .)
install(DIRECTORY offsets DESTINATION .)
