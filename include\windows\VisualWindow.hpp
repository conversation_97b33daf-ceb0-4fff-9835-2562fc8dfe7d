#pragma once

#include "../Window.hpp"

namespace FiveMMenu {

    class VisualWindow : public Window {
    public:
        VisualWindow();
        ~VisualWindow() override = default;

        void Render() override;
        void Update() override;

    private:
        void RenderESPSection();
        void RenderAimbotSection();
        void RenderWorldSection();
        void RenderEffectsSection();

        // ESP Settings
        bool m_espEnabled = false;
        bool m_espPlayers = true;
        bool m_espVehicles = true;
        bool m_espItems = false;
        bool m_espNPCs = false;
        float m_espDistance = 500.0f;
        float m_espBoxColor[4] = {1.0f, 0.0f, 0.0f, 1.0f};
        float m_espTextColor[4] = {1.0f, 1.0f, 1.0f, 1.0f};
        bool m_espShowDistance = true;
        bool m_espShowHealth = true;
        bool m_espShowName = true;
        bool m_espLines = false;

        // Aimbot Settings
        bool m_aimbotEnabled = false;
        bool m_aimbotVisibleOnly = true;
        bool m_aimbotTeamCheck = true;
        float m_aimbotFOV = 90.0f;
        float m_aimbotSmooth = 5.0f;
        int m_aimbotBone = 0; // 0=Head, 1=Chest, 2=Stomach
        int m_aimbotKey = 1; // Mouse button
        bool m_aimbotAutoShoot = false;

        // World Modifications
        bool m_noFog = false;
        bool m_noRain = false;
        bool m_alwaysDay = false;
        bool m_noTraffic = false;
        bool m_noPeds = false;
        float m_timeScale = 1.0f;
        float m_gravityScale = 1.0f;

        // Visual Effects
        bool m_nightVision = false;
        bool m_thermalVision = false;
        bool m_crosshair = false;
        float m_crosshairColor[4] = {0.0f, 1.0f, 0.0f, 1.0f};
        int m_crosshairSize = 10;
        bool m_fpsCounter = false;
        bool m_speedometer = false;

        // Camera
        float m_fov = 90.0f;
        bool m_freeCam = false;
        float m_freeCamSpeed = 1.0f;
    };

} // namespace FiveMMenu
