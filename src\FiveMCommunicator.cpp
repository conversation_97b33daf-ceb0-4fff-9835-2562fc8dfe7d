#include "FiveMCommunicator.hpp"
#include <iostream>
#include <chrono>
#include <thread>

#ifdef _WIN32
#include <windows.h>
#endif

namespace FiveMMenu {

    FiveMCommunicator::FiveMCommunicator()
        : m_isConnected(false)
        , m_shouldStop(false)
#ifdef _WIN32
        , m_pipeHandle(INVALID_HANDLE_VALUE)
#endif
    {
    }

    FiveMCommunicator::~FiveMCommunicator() {
        Shutdown();
    }

    bool FiveMCommunicator::Initialize() {
        m_shouldStop = false;
        
        // Iniciar thread de comunicação
        m_communicationThread = std::thread(&FiveMCommunicator::ProcessCommands, this);
        
        return ConnectToFiveM();
    }

    void FiveMCommunicator::Shutdown() {
        m_shouldStop = true;
        
        if (m_communicationThread.joinable()) {
            m_communicationThread.join();
        }
        
        DisconnectFromFiveM();
    }

    void FiveMCommunicator::TeleportPlayer(float x, float y, float z) {
        Command cmd;
        cmd.type = "teleport";
        cmd.params = {x, y, z};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SpawnVehicle(const std::string& model) {
        Command cmd;
        cmd.type = "spawn_vehicle";
        cmd.data = model;
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetPlayerHealth(int health) {
        Command cmd;
        cmd.type = "set_health";
        cmd.params = {static_cast<float>(health)};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetPlayerArmor(int armor) {
        Command cmd;
        cmd.type = "set_armor";
        cmd.params = {static_cast<float>(armor)};
        SendCommand(cmd);
    }

    void FiveMCommunicator::GiveMoney(int amount) {
        Command cmd;
        cmd.type = "give_money";
        cmd.params = {static_cast<float>(amount)};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetWeather(const std::string& weather) {
        Command cmd;
        cmd.type = "set_weather";
        cmd.data = weather;
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetTime(int hour, int minute) {
        Command cmd;
        cmd.type = "set_time";
        cmd.params = {static_cast<float>(hour), static_cast<float>(minute)};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetGodMode(bool enabled) {
        Command cmd;
        cmd.type = "set_godmode";
        cmd.params = {enabled ? 1.0f : 0.0f};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetInvisible(bool enabled) {
        Command cmd;
        cmd.type = "set_invisible";
        cmd.params = {enabled ? 1.0f : 0.0f};
        SendCommand(cmd);
    }

    void FiveMCommunicator::SetNoClip(bool enabled) {
        Command cmd;
        cmd.type = "set_noclip";
        cmd.params = {enabled ? 1.0f : 0.0f};
        SendCommand(cmd);
    }

    void FiveMCommunicator::RepairVehicle() {
        Command cmd;
        cmd.type = "repair_vehicle";
        SendCommand(cmd);
    }

    void FiveMCommunicator::DeleteVehicle() {
        Command cmd;
        cmd.type = "delete_vehicle";
        SendCommand(cmd);
    }

    bool FiveMCommunicator::SendCommand(const Command& command) {
        if (!m_isConnected) {
            std::cout << "Não conectado ao FiveM. Comando ignorado: " << command.type << std::endl;
            return false;
        }

        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_commandQueue.push(command);
        return true;
    }

    void FiveMCommunicator::ProcessCommands() {
        while (!m_shouldStop) {
            // Tentar conectar se não estiver conectado
            if (!m_isConnected) {
                if (ConnectToFiveM()) {
                    std::cout << "Conectado ao FiveM com sucesso!" << std::endl;
                    if (m_connectionCallback) {
                        m_connectionCallback(true);
                    }
                } else {
                    std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_INTERVAL_MS));
                    continue;
                }
            }

            // Processar comandos na fila
            std::queue<Command> localQueue;
            {
                std::lock_guard<std::mutex> lock(m_queueMutex);
                localQueue.swap(m_commandQueue);
            }

            while (!localQueue.empty() && m_isConnected) {
                const Command& cmd = localQueue.front();
                
#ifdef _WIN32
                // Construir mensagem para enviar
                std::string message = cmd.type;
                if (!cmd.data.empty()) {
                    message += "|" + cmd.data;
                }
                for (float param : cmd.params) {
                    message += "|" + std::to_string(param);
                }
                message += "\n";

                DWORD bytesWritten;
                BOOL success = WriteFile(
                    m_pipeHandle,
                    message.c_str(),
                    static_cast<DWORD>(message.length()),
                    &bytesWritten,
                    nullptr
                );

                if (!success || bytesWritten != message.length()) {
                    std::cout << "Erro ao enviar comando. Desconectando..." << std::endl;
                    DisconnectFromFiveM();
                    if (m_connectionCallback) {
                        m_connectionCallback(false);
                    }
                    break;
                }
#endif
                
                localQueue.pop();
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    bool FiveMCommunicator::ConnectToFiveM() {
#ifdef _WIN32
        m_pipeHandle = CreateFileA(
            PIPE_NAME,
            GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            0,
            nullptr
        );

        if (m_pipeHandle == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            if (error != ERROR_PIPE_BUSY) {
                return false;
            }

            // Pipe está ocupado, tentar esperar
            if (!WaitNamedPipeA(PIPE_NAME, 5000)) {
                return false;
            }

            m_pipeHandle = CreateFileA(
                PIPE_NAME,
                GENERIC_WRITE,
                0,
                nullptr,
                OPEN_EXISTING,
                0,
                nullptr
            );

            if (m_pipeHandle == INVALID_HANDLE_VALUE) {
                return false;
            }
        }

        // Configurar pipe para modo de mensagem
        DWORD mode = PIPE_READMODE_MESSAGE;
        BOOL success = SetNamedPipeHandleState(
            m_pipeHandle,
            &mode,
            nullptr,
            nullptr
        );

        if (!success) {
            CloseHandle(m_pipeHandle);
            m_pipeHandle = INVALID_HANDLE_VALUE;
            return false;
        }

        m_isConnected = true;
        return true;
#else
        // Implementação para Linux/Mac se necessário
        return false;
#endif
    }

    void FiveMCommunicator::DisconnectFromFiveM() {
#ifdef _WIN32
        if (m_pipeHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(m_pipeHandle);
            m_pipeHandle = INVALID_HANDLE_VALUE;
        }
#endif
        m_isConnected = false;
    }

} // namespace FiveMMenu
