#pragma once

#include "../Window.hpp"
#include <vector>
#include <string>

namespace FiveMMenu {

    struct TeleportLocation {
        std::string name;
        float x, y, z;
        std::string category;
        std::string description;
    };

    class TeleportWindow : public Window {
    public:
        TeleportWindow();
        ~TeleportWindow() override = default;

        void Render() override;
        void Update() override;

    private:
        void RenderQuickTeleport();
        void RenderLocationBrowser();
        void RenderCustomCoordinates();
        void RenderWaypoint();
        void RenderFavorites();
        void LoadTeleportLocations();

        // Localizações predefinidas
        std::vector<TeleportLocation> m_locations;
        std::vector<TeleportLocation> m_filteredLocations;
        std::vector<TeleportLocation> m_favoriteLocations;

        // Categorias
        std::vector<std::string> m_categories = {
            "Todos", "Aeroportos", "Hosp<PERSON>is", "<PERSON><PERSON><PERSON><PERSON>", "Lojas",
            "Pontos Turísticos", "Gara<PERSON>", "<PERSON>õ<PERSON>", "Outros"
        };

        int m_selectedCategory = 0;
        int m_selectedLocation = 0;
        char m_searchBuffer[256] = "";

        // Coordenadas customizadas
        float m_customX = 0.0f;
        float m_customY = 0.0f;
        float m_customZ = 0.0f;
        char m_customName[256] = "";

        // Configurações
        bool m_teleportVehicle = true;
        bool m_safeHeight = true;
        bool m_fadeScreen = true;
        
        // Waypoint
        bool m_hasWaypoint = false;
        float m_waypointX = 0.0f;
        float m_waypointY = 0.0f;
        float m_waypointZ = 0.0f;

        // Histórico
        std::vector<TeleportLocation> m_teleportHistory;
        static constexpr size_t MAX_HISTORY = 10;
    };

} // namespace FiveMMenu
