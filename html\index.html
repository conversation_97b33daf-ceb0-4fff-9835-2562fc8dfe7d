<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Externo FiveM</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="menu-container" class="hidden">
        <div class="menu-header">
            <h1><i class="fas fa-cog"></i> Menu Administrativo</h1>
            <button id="close-menu" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="menu-content">
            <div class="menu-sidebar">
                <nav class="menu-nav">
                    <button class="nav-btn active" data-tab="player">
                        <i class="fas fa-user"></i>
                        <span>Jogador</span>
                    </button>
                    <button class="nav-btn" data-tab="vehicle">
                        <i class="fas fa-car"></i>
                        <span>Veículos</span>
                    </button>
                    <button class="nav-btn" data-tab="teleport">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Teleporte</span>
                    </button>
                    <button class="nav-btn" data-tab="weather">
                        <i class="fas fa-cloud-sun"></i>
                        <span>Clima</span>
                    </button>
                    <button class="nav-btn" data-tab="admin">
                        <i class="fas fa-shield-alt"></i>
                        <span>Admin</span>
                    </button>
                </nav>
            </div>
            
            <div class="menu-main">
                <!-- Aba Jogador -->
                <div id="player-tab" class="tab-content active">
                    <h2>Configurações do Jogador</h2>
                    <div class="option-group">
                        <h3>Saúde & Armadura</h3>
                        <button class="action-btn" onclick="setHealth(100)">
                            <i class="fas fa-heart"></i> Vida Completa
                        </button>
                        <button class="action-btn" onclick="setArmor(100)">
                            <i class="fas fa-shield"></i> Armadura Completa
                        </button>
                    </div>
                    
                    <div class="option-group">
                        <h3>Dinheiro</h3>
                        <div class="input-group">
                            <input type="number" id="money-amount" placeholder="Quantidade" min="0">
                            <button class="action-btn" onclick="giveMoney()">
                                <i class="fas fa-dollar-sign"></i> Adicionar Dinheiro
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Aba Veículos -->
                <div id="vehicle-tab" class="tab-content">
                    <h2>Spawn de Veículos</h2>
                    <div class="option-group">
                        <h3>Veículos Populares</h3>
                        <div class="vehicle-grid">
                            <button class="vehicle-btn" onclick="spawnVehicle('adder')">
                                <i class="fas fa-car"></i> Adder
                            </button>
                            <button class="vehicle-btn" onclick="spawnVehicle('zentorno')">
                                <i class="fas fa-car"></i> Zentorno
                            </button>
                            <button class="vehicle-btn" onclick="spawnVehicle('t20')">
                                <i class="fas fa-car"></i> T20
                            </button>
                            <button class="vehicle-btn" onclick="spawnVehicle('insurgent')">
                                <i class="fas fa-truck"></i> Insurgent
                            </button>
                        </div>
                    </div>
                    
                    <div class="option-group">
                        <h3>Spawn Customizado</h3>
                        <div class="input-group">
                            <input type="text" id="vehicle-name" placeholder="Nome do veículo">
                            <button class="action-btn" onclick="spawnCustomVehicle()">
                                <i class="fas fa-plus"></i> Spawnar
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Aba Teleporte -->
                <div id="teleport-tab" class="tab-content">
                    <h2>Teleporte</h2>
                    <div class="option-group">
                        <h3>Locais Populares</h3>
                        <div class="teleport-grid">
                            <button class="teleport-btn" onclick="teleportTo(-1037.5, -2737.6, 20.2)">
                                <i class="fas fa-plane"></i> Aeroporto
                            </button>
                            <button class="teleport-btn" onclick="teleportTo(425.1, -979.5, 30.7)">
                                <i class="fas fa-hospital"></i> Hospital
                            </button>
                            <button class="teleport-btn" onclick="teleportTo(1855.1, 3683.9, 34.3)">
                                <i class="fas fa-warehouse"></i> Sandy Shores
                            </button>
                            <button class="teleport-btn" onclick="teleportTo(-1155.4, -1519.9, 10.6)">
                                <i class="fas fa-anchor"></i> Porto
                            </button>
                        </div>
                    </div>
                    
                    <div class="option-group">
                        <h3>Coordenadas Customizadas</h3>
                        <div class="coord-inputs">
                            <input type="number" id="coord-x" placeholder="X" step="0.1">
                            <input type="number" id="coord-y" placeholder="Y" step="0.1">
                            <input type="number" id="coord-z" placeholder="Z" step="0.1">
                            <button class="action-btn" onclick="teleportToCustom()">
                                <i class="fas fa-location-arrow"></i> Teleportar
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Aba Clima -->
                <div id="weather-tab" class="tab-content">
                    <h2>Controle de Clima</h2>
                    <div class="option-group">
                        <h3>Tipos de Clima</h3>
                        <div class="weather-grid">
                            <button class="weather-btn" onclick="setWeather('CLEAR')">
                                <i class="fas fa-sun"></i> Ensolarado
                            </button>
                            <button class="weather-btn" onclick="setWeather('RAIN')">
                                <i class="fas fa-cloud-rain"></i> Chuva
                            </button>
                            <button class="weather-btn" onclick="setWeather('THUNDER')">
                                <i class="fas fa-bolt"></i> Tempestade
                            </button>
                            <button class="weather-btn" onclick="setWeather('FOGGY')">
                                <i class="fas fa-smog"></i> Neblina
                            </button>
                        </div>
                    </div>
                    
                    <div class="option-group">
                        <h3>Hora do Dia</h3>
                        <div class="time-controls">
                            <input type="range" id="time-slider" min="0" max="23" value="12">
                            <span id="time-display">12:00</span>
                            <button class="action-btn" onclick="setTime()">
                                <i class="fas fa-clock"></i> Definir Hora
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Aba Admin -->
                <div id="admin-tab" class="tab-content">
                    <h2>Ferramentas Administrativas</h2>
                    <div class="option-group">
                        <h3>Modo Invisível</h3>
                        <button class="action-btn toggle-btn" id="invisible-btn" onclick="toggleInvisible()">
                            <i class="fas fa-eye-slash"></i> Ativar Invisibilidade
                        </button>
                    </div>
                    
                    <div class="option-group">
                        <h3>Modo Deus</h3>
                        <button class="action-btn toggle-btn" id="godmode-btn" onclick="toggleGodMode()">
                            <i class="fas fa-shield-alt"></i> Ativar Modo Deus
                        </button>
                    </div>
                    
                    <div class="option-group">
                        <h3>NoClip</h3>
                        <button class="action-btn toggle-btn" id="noclip-btn" onclick="toggleNoClip()">
                            <i class="fas fa-ghost"></i> Ativar NoClip
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
