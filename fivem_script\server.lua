-- Servidor do menu externo para FiveM

-- Event handler para dar dinheiro
RegisterNetEvent('external_menu:giveMoney')
AddEventHandler('external_menu:giveMoney', function(amount)
    local source = source
    local player = GetPlayerName(source)
    
    -- Verificar se o jogador tem permissão (implementar seu sistema de permissões)
    if IsPlayerAdmin(source) then
        -- Aqui você implementaria a lógica do seu framework (ESX, QBCore, etc.)
        -- Exemplo genérico:
        
        -- Para ESX:
        -- local xPlayer = ESX.GetPlayerFromId(source)
        -- xPlayer.addMoney(amount)
        
        -- Para QBCore:
        -- local Player = QBCore.Functions.GetPlayer(source)
        -- Player.Functions.AddMoney('cash', amount)
        
        print(string.format("Dinheiro adicionado: %s recebeu $%d", player, amount))
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 255, 0},
            multiline = true,
            args = {"Menu Externo", string.format("Você recebeu $%d", amount)}
        })
    else
        print(string.format("Tentativa não autorizada de adicionar dinheiro: %s", player))
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Menu Externo", "Você não tem permissão para usar este comando"}
        })
    end
end)

-- Função para verificar se o jogador é admin
function IsPlayerAdmin(source)
    -- Implementar sua lógica de verificação de admin aqui
    -- Exemplo simples:
    local identifiers = GetPlayerIdentifiers(source)
    local adminList = {
        "steam:110000100000000", -- Substitua pelos IDs dos admins
        "license:1234567890abcdef"
    }
    
    for _, id in pairs(identifiers) do
        for _, adminId in pairs(adminList) do
            if id == adminId then
                return true
            end
        end
    end
    
    return false
end

-- Event handler para comandos administrativos
RegisterNetEvent('external_menu:adminCommand')
AddEventHandler('external_menu:adminCommand', function(command, target, data)
    local source = source
    
    if not IsPlayerAdmin(source) then
        return
    end
    
    if command == "kick" then
        DropPlayer(target, data or "Kickado por um administrador")
    elseif command == "ban" then
        -- Implementar sistema de ban
        print(string.format("Jogador %d banido por %s", target, GetPlayerName(source)))
    elseif command == "teleport_to_player" then
        local targetCoords = GetEntityCoords(GetPlayerPed(target))
        TriggerClientEvent('external_menu:executeCommand', source, 'teleport', '', {targetCoords.x, targetCoords.y, targetCoords.z})
    elseif command == "bring_player" then
        local sourceCoords = GetEntityCoords(GetPlayerPed(source))
        TriggerClientEvent('external_menu:executeCommand', target, 'teleport', '', {sourceCoords.x, sourceCoords.y, sourceCoords.z})
    end
end)

-- Comando para recarregar o menu externo
RegisterCommand('reload_external_menu', function(source, args)
    if source == 0 or IsPlayerAdmin(source) then
        TriggerClientEvent('external_menu:reload', -1)
        print("Menu externo recarregado para todos os jogadores")
    end
end, true)

-- Log de conexões
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print(string.format("Jogador conectando: %s (ID: %d)", name, source))
end)

AddEventHandler('playerDropped', function(reason)
    local source = source
    print(string.format("Jogador desconectado: %s (Razão: %s)", GetPlayerName(source), reason))
end)

-- Sistema de logs para comandos do menu externo
function LogExternalMenuAction(player, action, details)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local logEntry = string.format("[%s] %s executou: %s (%s)", timestamp, player, action, details or "")
    
    -- Salvar em arquivo ou banco de dados
    print("EXTERNAL_MENU_LOG: " .. logEntry)
    
    -- Opcional: enviar para webhook do Discord
    -- SendToDiscordWebhook(logEntry)
end

-- Event para registrar ações
RegisterNetEvent('external_menu:logAction')
AddEventHandler('external_menu:logAction', function(action, details)
    local source = source
    local playerName = GetPlayerName(source)
    LogExternalMenuAction(playerName, action, details)
end)
