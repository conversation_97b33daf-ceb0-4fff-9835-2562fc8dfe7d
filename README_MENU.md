# 🎮 SKECH FiveM Cheat Menu

Menu de cheat para FiveM baseado no design do Figma, implementado em C++ com ImGui e DirectX 11.

## 🎨 **Design**

Este menu é uma **cópia pixel-perfect** do design do Figma:
- **Tema escuro** com cores exatas (#141414, #1E1E1E)
- **Cor de destaque vermelha/laranja** (#FF4D33)
- **Layout sidebar + conteúdo principal**
- **Toggles, sliders e checkboxes customizados**
- **Tipografia e espaçamentos idênticos**

## 📋 **Funcionalidades Implementadas**

### ✅ **Interface Completa**
- ✅ Sidebar navegável com seções
- ✅ Layout responsivo
- ✅ Toggles customizados (estilo Figma)
- ✅ Sliders com cor vermelha
- ✅ Checkboxes personalizados
- ✅ Color pickers
- ✅ Animações suaves

### ✅ **Seções do Menu**
- ✅ **Aimbot** - Configurações completas
- ✅ **Silent Aim** - Sistema silencioso
- ✅ **Magic Bullet** - <PERSON><PERSON> m<PERSON>
- ✅ **Triggerbot** - Disparo automático
- 🔄 **Player** - Em desenvolvimento
- 🔄 **Visuals** - ESP e visuais
- 🔄 **Miscellaneous** - Recursos diversos
- 🔄 **Configs** - Salvar/carregar

### ✅ **Controles**
- ✅ **INSERT** - Abrir/fechar menu
- ✅ **Mouse** - Navegação completa
- ✅ **Hotkeys** - Configuráveis

## 🚀 **Como Compilar e Usar**

### **Passo 1: Baixar ImGui**
```bash
# Execute o script para baixar ImGui automaticamente
.\download_imgui.bat
```

### **Passo 2: Compilar o Menu**
```bash
# Execute o script de build
.\build_menu.bat
```

### **Passo 3: Executar**
```bash
# Execute o menu compilado
.\build_menu\Release\FiveMCheatMenu.exe
```

## 🎯 **Controles do Menu**

| Tecla | Ação |
|-------|------|
| **INSERT** | Abrir/Fechar menu |
| **Mouse** | Navegar e interagir |
| **ESC** | Fechar aplicação |

## 📁 **Estrutura do Projeto**

```
├── src/
│   ├── ImGuiMenu.hpp          # Header da classe do menu
│   ├── ImGuiMenu.cpp          # Implementação do menu
│   └── menu_main.cpp          # Aplicação principal
├── vendor/
│   └── imgui/                 # Biblioteca ImGui
├── build_menu/                # Arquivos de build
├── download_imgui.bat         # Script para baixar ImGui
├── build_menu.bat             # Script de compilação
└── README_MENU.md             # Este arquivo
```

## 🎨 **Capturas de Tela**

O menu implementa exatamente o design do Figma:

### **Seção Aimbot**
- Toggle principal com cor vermelha
- Configurações de hotkey (Mouse 2)
- Checkboxes para Target Peds, Visible Check
- Sliders para FOV, Smooth, Curve, Aim Distance
- Seção HitBox com opções

### **Seção Silent Aim**
- Toggle com ícone 🎯
- Configurações de Silent Aim Hotkey
- Field Of View slider
- HitBox configurável
- FOV Circle toggle

### **Seção Magic Bullet**
- Toggle com ícone 🔮
- Magic Bullet Hotkey
- Field Of View configurável
- Color picker para FOV Color

### **Seção Triggerbot**
- Use Hotkey toggle
- Target Players/Peds/Dead
- Delay slider configurável

## 🔧 **Requisitos Técnicos**

- **Windows 10/11**
- **Visual Studio 2022** (Community/Professional/Enterprise)
- **DirectX 11** (incluído no Windows)
- **CMake 3.16+** (opcional, incluído no VS)

## 🎯 **Próximos Passos**

1. **✅ Interface base** - Concluída
2. **🔄 Integração com offsets** - Usar offsets do dumper
3. **🔄 Funcionalidades de cheat** - Implementar aimbot real
4. **🔄 ESP system** - Sistema de ESP
5. **🔄 Configurações** - Save/load configs

## 🚨 **Aviso Legal**

Este projeto é apenas para fins educacionais e de pesquisa. O uso em servidores públicos pode violar os termos de serviço.

## 📞 **Suporte**

Se encontrar problemas:

1. **Verifique se o ImGui foi baixado** corretamente
2. **Certifique-se de que o Visual Studio está instalado**
3. **Execute os scripts como administrador** se necessário
4. **Verifique se o DirectX 11 está disponível**

---

**🎮 SKECH [beta] for FiveM - Interface baseada no design do Figma**
