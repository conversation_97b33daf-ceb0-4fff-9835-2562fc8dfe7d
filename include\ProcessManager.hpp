#pragma once

#include <windows.h>
#include <tlhelp32.h>
#include <string>
#include <vector>
#include <memory>

namespace FiveMCheat {

    struct ProcessInfo {
        DWORD processId;
        HANDLE processHandle;
        std::string processName;
        uintptr_t baseAddress;
        size_t moduleSize;
    };

    class ProcessManager {
    public:
        ProcessManager();
        ~ProcessManager();

        // Process management
        bool AttachToProcess(const std::string& processName);
        bool AttachToProcess(DWORD processId);
        void DetachFromProcess();
        bool IsAttached() const { return m_processInfo.processHandle != nullptr; }

        // Process information
        const ProcessInfo& GetProcessInfo() const { return m_processInfo; }
        DWORD GetProcessId() const { return m_processInfo.processId; }
        HANDLE GetProcessHandle() const { return m_processInfo.processHandle; }
        uintptr_t GetBaseAddress() const { return m_processInfo.baseAddress; }

        // Memory operations
        bool ReadMemory(uintptr_t address, void* buffer, size_t size);
        bool WriteMemory(uintptr_t address, const void* buffer, size_t size);
        
        template<typename T>
        T ReadMemory(uintptr_t address) {
            T value{};
            ReadMemory(address, &value, sizeof(T));
            return value;
        }

        template<typename T>
        bool WriteMemory(uintptr_t address, const T& value) {
            return WriteMemory(address, &value, sizeof(T));
        }

        // Module operations
        uintptr_t GetModuleBaseAddress(const std::string& moduleName);
        size_t GetModuleSize(const std::string& moduleName);

        // Anti-detection features
        void EnableAntiDetection();
        void DisableAntiDetection();
        bool IsProcessProtected();

        // Utility functions
        std::vector<DWORD> FindProcessesByName(const std::string& processName);
        bool IsProcessRunning(const std::string& processName);
        bool ElevatePrivileges();

    private:
        ProcessInfo m_processInfo;
        bool m_antiDetectionEnabled;
        
        // Internal methods
        bool OpenProcessWithPrivileges(DWORD processId);
        void CloseProcessHandle();
        bool SetDebugPrivileges();
        void HideFromDebugger();
        void PatchNtQueryInformationProcess();

        // Anti-detection techniques
        static LONG WINAPI VectoredExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);
        void InstallExceptionHandler();
        void RemoveExceptionHandler();
        PVOID m_exceptionHandler;
    };

} // namespace FiveMCheat
