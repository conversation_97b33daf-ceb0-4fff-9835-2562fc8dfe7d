#pragma once

#include <string>
#include <vector>
#include <functional>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>

namespace FiveMMenu {

    struct Command {
        std::string type;
        std::string data;
        std::vector<float> params;
    };

    class FiveMCommunicator {
    public:
        FiveMCommunicator();
        ~FiveMCommunicator();

        bool Initialize();
        void Shutdown();

        // Comandos básicos
        void TeleportPlayer(float x, float y, float z);
        void SpawnVehicle(const std::string& model);
        void SetPlayerHealth(int health);
        void SetPlayerArmor(int armor);
        void GiveMoney(int amount);
        void SetWeather(const std::string& weather);
        void SetTime(int hour, int minute);

        // Comandos avançados
        void SetGodMode(bool enabled);
        void SetInvisible(bool enabled);
        void SetNoClip(bool enabled);
        void RepairVehicle();
        void DeleteVehicle();

        // Sistema de comunicação
        bool SendCommand(const Command& command);
        bool IsConnected() const { return m_isConnected; }

        // Callbacks
        using ConnectionCallback = std::function<void(bool)>;
        void SetConnectionCallback(ConnectionCallback callback) { m_connectionCallback = callback; }

    private:
        void ProcessCommands();
        bool ConnectToFiveM();
        void DisconnectFromFiveM();

        std::atomic<bool> m_isConnected;
        std::atomic<bool> m_shouldStop;
        std::thread m_communicationThread;
        
        std::queue<Command> m_commandQueue;
        std::mutex m_queueMutex;
        
        ConnectionCallback m_connectionCallback;

        // Configurações de conexão
        static constexpr const char* PIPE_NAME = "\\\\.\\pipe\\fivem_external_menu";
        static constexpr int RECONNECT_INTERVAL_MS = 5000;
        
#ifdef _WIN32
        void* m_pipeHandle; // HANDLE
#endif
    };

} // namespace FiveMMenu
