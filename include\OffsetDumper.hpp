#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <fstream>
#include <chrono>

namespace FiveMCheat {

    class ProcessManager;

    struct Signature {
        std::string name;
        std::string pattern;
        std::string mask;
        std::string module;
        bool isRelative;
        int operandLocation;
        int operandLength;
        int additionalOffset;
        std::string description;
        bool required;
    };

    struct DumpedOffset {
        std::string name;
        uintptr_t address;
        uintptr_t rva; // Relative Virtual Address
        std::string module;
        bool found;
        std::string description;
        std::chrono::system_clock::time_point timestamp;
    };

    struct ModuleInfo {
        std::string name;
        std::string path;
        uintptr_t baseAddress;
        size_t size;
        bool loaded;
    };

    struct DumperConfig {
        std::string gameName;
        std::string gameExecutable;
        std::string outputDirectory;
        std::string headerFileName;
        bool generateHeader;
        bool generateCheatTable;
        bool generateReClassFile;
        bool relativeByDefault;
        bool fileOnly;
        std::string diskFilePath;
        std::vector<std::string> additionalModules;
        std::vector<Signature> signatures;
        bool verboseOutput;
        int maxRetries;
        int retryDelay;
    };

    enum class OutputFormat {
        CppHeader,
        CheatEngineTable,
        ReClassNet,
        JSON,
        All
    };

    class OffsetDumper {
    public:
        OffsetDumper();
        ~OffsetDumper();

        // Configuration
        bool LoadConfig(const std::string& configFile);
        bool SaveConfig(const std::string& configFile);
        void SetConfig(const DumperConfig& config) { m_config = config; }
        const DumperConfig& GetConfig() const { return m_config; }

        // Main dumping functions
        bool DumpOffsets();
        bool DumpFromRuntime();
        bool DumpFromDisk();
        bool DumpFromMemoryDump(const std::string& dumpFile);

        // Signature management
        void AddSignature(const Signature& signature);
        void RemoveSignature(const std::string& name);
        void ClearSignatures();
        std::vector<Signature> GetSignatures() const { return m_config.signatures; }

        // Results
        std::vector<DumpedOffset> GetDumpedOffsets() const { return m_dumpedOffsets; }
        DumpedOffset GetOffset(const std::string& name);
        bool IsOffsetFound(const std::string& name);
        uintptr_t GetOffsetAddress(const std::string& name);

        // Output generation
        bool GenerateOutputs();
        bool GenerateCppHeader();
        bool GenerateCheatEngineTable();
        bool GenerateReClassFile();
        bool GenerateJSONOutput();

        // Utility functions
        bool ValidateSignatures();
        void PrintResults();
        void PrintStatistics();
        std::string GetLastError() const { return m_lastError; }

        // FiveM specific presets
        void LoadFiveMSignatures();
        void LoadGTAVSignatures();
        void LoadRageEngineSignatures();

    private:
        DumperConfig m_config;
        std::shared_ptr<ProcessManager> m_processManager;
        std::vector<DumpedOffset> m_dumpedOffsets;
        std::vector<ModuleInfo> m_modules;
        std::string m_lastError;
        
        // Statistics
        int m_totalSignatures;
        int m_foundSignatures;
        int m_failedSignatures;
        std::chrono::steady_clock::time_point m_startTime;
        std::chrono::steady_clock::time_point m_endTime;

        // Internal methods
        bool InitializeProcess();
        bool LoadModules();
        bool ProcessSignatures();
        bool ProcessSignature(const Signature& signature);
        
        // Pattern scanning
        uintptr_t FindPattern(const std::string& pattern, const std::string& mask, 
                             const std::string& moduleName = "");
        uintptr_t FindPatternInModule(const std::string& pattern, const std::string& mask, 
                                     const ModuleInfo& module);
        uintptr_t FindPatternInMemory(const std::vector<uint8_t>& memory, 
                                     const std::string& pattern, const std::string& mask);
        
        // Address resolution
        uintptr_t ResolveRelativeAddress(uintptr_t patternAddress, const Signature& signature);
        uintptr_t ReadRelativeAddress(uintptr_t address, int operandLocation, int operandLength);
        
        // File operations
        bool ReadFileToMemory(const std::string& filePath, std::vector<uint8_t>& buffer);
        bool WriteStringToFile(const std::string& filePath, const std::string& content);
        
        // Pattern utilities
        std::vector<uint8_t> PatternToBytes(const std::string& pattern);
        std::string BytesToPattern(const std::vector<uint8_t>& bytes);
        bool ComparePattern(const uint8_t* data, const std::vector<uint8_t>& pattern, 
                           const std::string& mask);
        
        // Module management
        ModuleInfo GetModuleInfo(const std::string& moduleName);
        bool IsModuleLoaded(const std::string& moduleName);
        uintptr_t GetModuleBase(const std::string& moduleName);
        size_t GetModuleSize(const std::string& moduleName);
        
        // Output formatting
        std::string FormatCppHeader();
        std::string FormatCheatEngineTable();
        std::string FormatReClassFile();
        std::string FormatJSONOutput();
        
        // Helper functions
        std::string GetTimestamp();
        std::string AddressToString(uintptr_t address);
        std::string GetRelativeAddress(uintptr_t address, const std::string& moduleName);
        bool CreateDirectory(const std::string& path);
        
        // Error handling
        void SetError(const std::string& error);
        void LogMessage(const std::string& message);
        void LogError(const std::string& error);
        void LogWarning(const std::string& warning);
        
        // Validation
        bool ValidateSignature(const Signature& signature);
        bool ValidateConfig();
        bool ValidateOutputPath();
    };

    // Utility functions for config loading
    namespace ConfigLoader {
        DumperConfig LoadFromJSON(const std::string& jsonFile);
        bool SaveToJSON(const DumperConfig& config, const std::string& jsonFile);
        Signature ParseSignature(const std::string& jsonSignature);
        std::string SignatureToJSON(const Signature& signature);
    }

    // Predefined signature sets
    namespace SignatureSets {
        std::vector<Signature> GetFiveMSignatures();
        std::vector<Signature> GetGTAVSignatures();
        std::vector<Signature> GetRageEngineSignatures();
        std::vector<Signature> GetCommonGameSignatures();
    }

} // namespace FiveMCheat
